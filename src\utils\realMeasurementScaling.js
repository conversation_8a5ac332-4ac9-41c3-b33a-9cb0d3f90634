/**
 * Real Measurement Scaling Module
 * 
 * This module provides accurate scaling using MediaPipe hand measurements
 * for realistic watch-to-wrist size matching and resolution scaling.
 */

import { HAND_LANDMARKS } from './handDetection';

// Real-world measurement constants
const REAL_WORLD_CONSTANTS = {
  // Average adult hand measurements (in mm)
  AVERAGE_HAND_LENGTH: 189, // From wrist to middle finger tip
  AVERAGE_HAND_WIDTH: 84,   // Across knuckles
  AVERAGE_WRIST_WIDTH: 55,  // Wrist width (top view)
  AVERAGE_WRIST_HEIGHT: 45, // Wrist height (side view)
  
  // Measurement ratios for calibration
  HAND_TO_WRIST_RATIO: 1.53, // Hand width to wrist width ratio
  WRIST_CIRCUMFERENCE_TO_WIDTH_RATIO: 3.14, // Approximate for elliptical wrist
  
  // Watch size categories (in mm)
  WATCH_SIZES: {
    SMALL: { min: 32, max: 38 },
    MEDIUM: { min: 38, max: 42 },
    LARGE: { min: 42, max: 46 },
    EXTRA_LARGE: { min: 46, max: 50 }
  }
};

/**
 * Calculate real-world measurements from MediaPipe landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} options - Calibration options
 * @returns {Object} - Real-world measurements
 */
export const calculateRealWorldMeasurements = (landmarks, options = {}) => {
  const {
    calibrationMethod = 'statistical', // 'statistical', 'user_input', 'reference_object'
    userProvidedWristSize = null, // User-provided wrist size in mm
    referenceObjectSize = null    // Reference object size for calibration
  } = options;

  if (!landmarks || landmarks.length === 0) {
    return null;
  }

  // Calculate normalized measurements from landmarks
  const normalizedMeasurements = calculateNormalizedMeasurements(landmarks);
  
  // Convert to real-world measurements based on calibration method
  let realWorldMeasurements;
  
  switch (calibrationMethod) {
    case 'user_input':
      realWorldMeasurements = calibrateWithUserInput(normalizedMeasurements, userProvidedWristSize);
      break;
    case 'reference_object':
      realWorldMeasurements = calibrateWithReferenceObject(normalizedMeasurements, referenceObjectSize);
      break;
    case 'statistical':
    default:
      realWorldMeasurements = calibrateWithStatistics(normalizedMeasurements);
      break;
  }
  
  return {
    ...realWorldMeasurements,
    normalizedMeasurements,
    calibrationMethod,
    confidence: calculateMeasurementConfidence(landmarks, realWorldMeasurements)
  };
};

/**
 * Calculate normalized measurements from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Normalized measurements
 */
const calculateNormalizedMeasurements = (landmarks) => {
  // Get key landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  const middleTip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP];
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  const ringMCP = landmarks[HAND_LANDMARKS.RING_FINGER_MCP];
  
  // Calculate hand length (wrist to middle finger tip)
  const handLength = distance3D(wrist, middleTip);
  
  // Calculate hand width (across knuckles)
  const handWidth = distance3D(indexMCP, pinkyMCP);
  
  // Calculate wrist width (thumb base to pinky base)
  const wristWidth = distance3D(thumbCMC, pinkyMCP);
  
  // Calculate wrist height (approximate from hand geometry)
  const palmCenter = {
    x: (indexMCP.x + ringMCP.x) / 2,
    y: (indexMCP.y + ringMCP.y) / 2,
    z: (indexMCP.z + ringMCP.z) / 2
  };
  const wristHeight = distance3D(wrist, palmCenter);
  
  // Calculate finger lengths
  const fingerLengths = {
    thumb: calculateFingerLength(landmarks, 'thumb'),
    index: calculateFingerLength(landmarks, 'index'),
    middle: calculateFingerLength(landmarks, 'middle'),
    ring: calculateFingerLength(landmarks, 'ring'),
    pinky: calculateFingerLength(landmarks, 'pinky')
  };
  
  return {
    handLength,
    handWidth,
    wristWidth,
    wristHeight,
    fingerLengths
  };
};

/**
 * Calculate finger length from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {string} finger - Finger name
 * @returns {number} - Normalized finger length
 */
const calculateFingerLength = (landmarks, finger) => {
  let segments = [];
  
  switch (finger) {
    case 'thumb':
      segments = [
        landmarks[HAND_LANDMARKS.THUMB_CMC],
        landmarks[HAND_LANDMARKS.THUMB_MCP],
        landmarks[HAND_LANDMARKS.THUMB_IP],
        landmarks[HAND_LANDMARKS.THUMB_TIP]
      ];
      break;
    case 'index':
      segments = [
        landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP],
        landmarks[HAND_LANDMARKS.INDEX_FINGER_PIP],
        landmarks[HAND_LANDMARKS.INDEX_FINGER_DIP],
        landmarks[HAND_LANDMARKS.INDEX_FINGER_TIP]
      ];
      break;
    case 'middle':
      segments = [
        landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP],
        landmarks[HAND_LANDMARKS.MIDDLE_FINGER_PIP],
        landmarks[HAND_LANDMARKS.MIDDLE_FINGER_DIP],
        landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP]
      ];
      break;
    case 'ring':
      segments = [
        landmarks[HAND_LANDMARKS.RING_FINGER_MCP],
        landmarks[HAND_LANDMARKS.RING_FINGER_PIP],
        landmarks[HAND_LANDMARKS.RING_FINGER_DIP],
        landmarks[HAND_LANDMARKS.RING_FINGER_TIP]
      ];
      break;
    case 'pinky':
      segments = [
        landmarks[HAND_LANDMARKS.PINKY_MCP],
        landmarks[HAND_LANDMARKS.PINKY_PIP],
        landmarks[HAND_LANDMARKS.PINKY_DIP],
        landmarks[HAND_LANDMARKS.PINKY_TIP]
      ];
      break;
    default:
      return 0;
  }
  
  // Calculate total length by summing segment lengths
  let totalLength = 0;
  for (let i = 0; i < segments.length - 1; i++) {
    totalLength += distance3D(segments[i], segments[i + 1]);
  }
  
  return totalLength;
};

/**
 * Calibrate measurements using statistical averages
 * @param {Object} normalizedMeasurements - Normalized measurements
 * @returns {Object} - Real-world measurements in mm
 */
const calibrateWithStatistics = (normalizedMeasurements) => {
  // Use hand length as the primary calibration reference
  const scaleFactor = REAL_WORLD_CONSTANTS.AVERAGE_HAND_LENGTH / normalizedMeasurements.handLength;
  
  return {
    handLength: normalizedMeasurements.handLength * scaleFactor,
    handWidth: normalizedMeasurements.handWidth * scaleFactor,
    wristWidth: normalizedMeasurements.wristWidth * scaleFactor,
    wristHeight: normalizedMeasurements.wristHeight * scaleFactor,
    wristCircumference: normalizedMeasurements.wristWidth * scaleFactor * REAL_WORLD_CONSTANTS.WRIST_CIRCUMFERENCE_TO_WIDTH_RATIO,
    scaleFactor
  };
};

/**
 * Calibrate measurements using user-provided wrist size
 * @param {Object} normalizedMeasurements - Normalized measurements
 * @param {number} userWristSize - User-provided wrist size in mm
 * @returns {Object} - Real-world measurements in mm
 */
const calibrateWithUserInput = (normalizedMeasurements, userWristSize) => {
  if (!userWristSize) {
    return calibrateWithStatistics(normalizedMeasurements);
  }
  
  // Use wrist width as the calibration reference
  const scaleFactor = userWristSize / normalizedMeasurements.wristWidth;
  
  return {
    handLength: normalizedMeasurements.handLength * scaleFactor,
    handWidth: normalizedMeasurements.handWidth * scaleFactor,
    wristWidth: userWristSize,
    wristHeight: normalizedMeasurements.wristHeight * scaleFactor,
    wristCircumference: userWristSize * REAL_WORLD_CONSTANTS.WRIST_CIRCUMFERENCE_TO_WIDTH_RATIO,
    scaleFactor
  };
};

/**
 * Calibrate measurements using a reference object
 * @param {Object} normalizedMeasurements - Normalized measurements
 * @param {Object} referenceObject - Reference object with known size
 * @returns {Object} - Real-world measurements in mm
 */
const calibrateWithReferenceObject = (normalizedMeasurements, referenceObject) => {
  if (!referenceObject || !referenceObject.realSize || !referenceObject.measuredSize) {
    return calibrateWithStatistics(normalizedMeasurements);
  }
  
  // Calculate scale factor from reference object
  const scaleFactor = referenceObject.realSize / referenceObject.measuredSize;
  
  return {
    handLength: normalizedMeasurements.handLength * scaleFactor,
    handWidth: normalizedMeasurements.handWidth * scaleFactor,
    wristWidth: normalizedMeasurements.wristWidth * scaleFactor,
    wristHeight: normalizedMeasurements.wristHeight * scaleFactor,
    wristCircumference: normalizedMeasurements.wristWidth * scaleFactor * REAL_WORLD_CONSTANTS.WRIST_CIRCUMFERENCE_TO_WIDTH_RATIO,
    scaleFactor
  };
};

/**
 * Calculate measurement confidence based on hand pose quality
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} realWorldMeasurements - Calculated real-world measurements
 * @returns {number} - Confidence score (0-1)
 */
const calculateMeasurementConfidence = (landmarks, realWorldMeasurements) => {
  let confidence = 1.0;
  
  // Check if measurements are within reasonable ranges
  if (realWorldMeasurements.wristWidth < 40 || realWorldMeasurements.wristWidth > 80) {
    confidence *= 0.7; // Unusual wrist width
  }
  
  if (realWorldMeasurements.handLength < 150 || realWorldMeasurements.handLength > 230) {
    confidence *= 0.7; // Unusual hand length
  }
  
  // Check landmark visibility (z-coordinate indicates depth/visibility)
  const avgZ = landmarks.reduce((sum, landmark) => sum + Math.abs(landmark.z), 0) / landmarks.length;
  if (avgZ > 0.1) {
    confidence *= 0.8; // Hand is far from camera or partially occluded
  }
  
  // Check hand pose stability (could be enhanced with temporal analysis)
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  if (wrist.z > 0.05) {
    confidence *= 0.9; // Wrist is not clearly visible
  }
  
  return Math.max(0.1, confidence);
};

/**
 * Calculate optimal watch size for the detected wrist
 * @param {Object} realWorldMeasurements - Real-world measurements
 * @param {Object} options - Sizing options
 * @returns {Object} - Recommended watch sizing
 */
export const calculateOptimalWatchSize = (realWorldMeasurements, options = {}) => {
  const {
    style = 'balanced', // 'small', 'balanced', 'large', 'oversized'
    gender = 'unisex'   // 'men', 'women', 'unisex'
  } = options;
  
  if (!realWorldMeasurements) {
    return null;
  }
  
  const wristWidth = realWorldMeasurements.wristWidth;
  
  // Calculate recommended case diameter based on wrist width
  let recommendedDiameter;
  
  switch (style) {
    case 'small':
      recommendedDiameter = wristWidth * 0.65;
      break;
    case 'balanced':
      recommendedDiameter = wristWidth * 0.75;
      break;
    case 'large':
      recommendedDiameter = wristWidth * 0.85;
      break;
    case 'oversized':
      recommendedDiameter = wristWidth * 0.95;
      break;
    default:
      recommendedDiameter = wristWidth * 0.75;
  }
  
  // Adjust for gender preferences
  if (gender === 'women') {
    recommendedDiameter *= 0.9;
  } else if (gender === 'men') {
    recommendedDiameter *= 1.05;
  }
  
  // Determine size category
  let sizeCategory = 'MEDIUM';
  for (const [category, range] of Object.entries(REAL_WORLD_CONSTANTS.WATCH_SIZES)) {
    if (recommendedDiameter >= range.min && recommendedDiameter <= range.max) {
      sizeCategory = category;
      break;
    }
  }
  
  return {
    recommendedDiameter: Math.round(recommendedDiameter),
    sizeCategory,
    wristWidth: Math.round(wristWidth),
    fitRatio: recommendedDiameter / wristWidth,
    recommendations: generateSizeRecommendations(wristWidth, recommendedDiameter)
  };
};

/**
 * Generate size recommendations for different watch styles
 * @param {number} wristWidth - Wrist width in mm
 * @param {number} baseDiameter - Base recommended diameter
 * @returns {Object} - Size recommendations
 */
const generateSizeRecommendations = (wristWidth, baseDiameter) => {
  return {
    dress: Math.round(baseDiameter * 0.9),      // Smaller for dress watches
    sport: Math.round(baseDiameter * 1.1),      // Larger for sport watches
    smartwatch: Math.round(baseDiameter * 1.05), // Slightly larger for smartwatches
    vintage: Math.round(baseDiameter * 0.85),   // Smaller for vintage style
    oversized: Math.round(baseDiameter * 1.2)   // Much larger for oversized style
  };
};

/**
 * Calculate scaling factor for watch image based on real measurements
 * @param {Object} watchSpecs - Watch specifications
 * @param {Object} realWorldMeasurements - Real-world measurements
 * @param {Object} displayDimensions - Display dimensions
 * @returns {Object} - Scaling parameters
 */
export const calculateWatchScaling = (watchSpecs, realWorldMeasurements, displayDimensions) => {
  if (!watchSpecs || !realWorldMeasurements || !displayDimensions) {
    return { scaleX: 1, scaleY: 1, offsetX: 0, offsetY: 0 };
  }
  
  const watchDiameter = watchSpecs.caseDiameter || watchSpecs.dialSize || 42;
  const wristWidth = realWorldMeasurements.wristWidth;
  
  // Calculate the scale factor to match real-world proportions
  const realWorldScale = watchDiameter / wristWidth;
  
  // Convert to display coordinates
  const displayWristWidth = displayDimensions.wristWidth; // Width of wrist area in pixels
  const targetWatchWidth = displayWristWidth * realWorldScale;
  
  // Calculate scale factors
  const scaleX = targetWatchWidth / watchSpecs.imageWidth;
  const scaleY = targetWatchWidth / watchSpecs.imageHeight; // Maintain aspect ratio
  
  // Calculate positioning offset to center on wrist
  const offsetX = (displayDimensions.width - targetWatchWidth) / 2;
  const offsetY = (displayDimensions.height - targetWatchWidth) / 2;
  
  return {
    scaleX,
    scaleY,
    offsetX,
    offsetY,
    targetWidth: targetWatchWidth,
    targetHeight: targetWatchWidth,
    realWorldScale
  };
};

/**
 * Calculate 3D distance between two points
 * @param {Object} p1 - First point {x, y, z}
 * @param {Object} p2 - Second point {x, y, z}
 * @returns {number} - Distance
 */
const distance3D = (p1, p2) => {
  return Math.sqrt(
    Math.pow(p2.x - p1.x, 2) +
    Math.pow(p2.y - p1.y, 2) +
    Math.pow(p2.z - p1.z, 2)
  );
};

export default {
  calculateRealWorldMeasurements,
  calculateOptimalWatchSize,
  calculateWatchScaling,
  REAL_WORLD_CONSTANTS
};
