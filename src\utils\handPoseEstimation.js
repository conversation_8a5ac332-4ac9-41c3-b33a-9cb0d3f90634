/**
 * 2.5D Hand Pose Estimation Module
 * 
 * This module provides advanced hand pose estimation using MediaPipe landmarks
 * to calculate hand rotation, tilt, and depth for realistic watch positioning.
 */

import { HAND_LANDMARKS } from './handDetection';

/**
 * Calculate 2.5D hand pose from MediaPipe landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - 2.5D hand pose data
 */
export const calculate2_5DHandPose = (landmarks) => {
  if (!landmarks || landmarks.length === 0) {
    return null;
  }

  // Calculate wrist plane and orientation
  const wristPlane = calculateWristPlane(landmarks);
  
  // Calculate wrist dimensions
  const wristDimensions = calculateWristDimensions(landmarks);
  
  // Calculate hand orientation
  const handOrientation = calculateHandOrientation(landmarks);
  
  // Calculate depth information
  const depthInfo = calculateDepthInformation(landmarks);
  
  // Calculate perspective transformation matrix
  const perspectiveMatrix = calculatePerspectiveMatrix(landmarks, wristPlane);
  
  return {
    wristPlane,
    wristDimensions,
    handOrientation,
    depthInfo,
    perspectiveMatrix
  };
};

/**
 * Calculate the wrist plane from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Wrist plane data
 */
const calculateWristPlane = (landmarks) => {
  // Get key wrist landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  
  // Calculate vectors defining the wrist plane
  const wristToThumb = {
    x: thumbCMC.x - wrist.x,
    y: thumbCMC.y - wrist.y,
    z: thumbCMC.z - wrist.z
  };
  
  const wristToPinky = {
    x: pinkyMCP.x - wrist.x,
    y: pinkyMCP.y - wrist.y,
    z: pinkyMCP.z - wrist.z
  };
  
  // Calculate normal vector to the wrist plane (cross product)
  const normal = crossProduct(wristToThumb, wristToPinky);
  
  // Normalize the normal vector
  const normalizedNormal = normalizeVector(normal);
  
  // Calculate the center of the wrist
  const center = {
    x: (wrist.x + thumbCMC.x + pinkyMCP.x) / 3,
    y: (wrist.y + thumbCMC.y + pinkyMCP.y) / 3,
    z: (wrist.z + thumbCMC.z + pinkyMCP.z) / 3
  };
  
  // Calculate the wrist plane equation: ax + by + cz + d = 0
  const a = normalizedNormal.x;
  const b = normalizedNormal.y;
  const c = normalizedNormal.z;
  const d = -(a * center.x + b * center.y + c * center.z);
  
  // Calculate the wrist angle (angle between wrist plane and camera plane)
  const cameraPlaneNormal = { x: 0, y: 0, z: 1 }; // Camera looks along z-axis
  const angle = angleBetweenVectors(normalizedNormal, cameraPlaneNormal);
  
  return {
    center,
    normal: normalizedNormal,
    equation: { a, b, c, d },
    angle
  };
};

/**
 * Calculate wrist dimensions from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Wrist dimensions
 */
const calculateWristDimensions = (landmarks) => {
  // Get key wrist landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  
  // Calculate wrist width (distance between thumb base and pinky base)
  const wristWidth = distance3D(thumbCMC, pinkyMCP);
  
  // Calculate wrist height (approximate as distance from wrist to middle of palm)
  const palmCenter = {
    x: (indexMCP.x + pinkyMCP.x) / 2,
    y: (indexMCP.y + pinkyMCP.y) / 2,
    z: (indexMCP.z + pinkyMCP.z) / 2
  };
  const wristHeight = distance3D(wrist, palmCenter);
  
  // Calculate wrist depth (using z-coordinates)
  const wristDepth = Math.abs(thumbCMC.z - pinkyMCP.z);
  
  // Calculate wrist circumference (approximate as ellipse perimeter)
  // Using Ramanujan's approximation for ellipse perimeter
  const a = wristWidth / 2; // Semi-major axis
  const b = wristHeight / 2; // Semi-minor axis
  const h = Math.pow((a - b) / (a + b), 2);
  const wristCircumference = Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
  
  return {
    width: wristWidth,
    height: wristHeight,
    depth: wristDepth,
    circumference: wristCircumference
  };
};

/**
 * Calculate hand orientation from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Hand orientation angles
 */
const calculateHandOrientation = (landmarks) => {
  // Get key landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const middleMCP = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
  const middleTip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP];
  
  // Calculate hand direction vector (from wrist to middle finger MCP)
  const handDirection = {
    x: middleMCP.x - wrist.x,
    y: middleMCP.y - wrist.y,
    z: middleMCP.z - wrist.z
  };
  
  // Calculate finger direction vector (from middle finger MCP to tip)
  const fingerDirection = {
    x: middleTip.x - middleMCP.x,
    y: middleTip.y - middleMCP.y,
    z: middleTip.z - middleMCP.z
  };
  
  // Normalize vectors
  const normalizedHandDirection = normalizeVector(handDirection);
  const normalizedFingerDirection = normalizeVector(fingerDirection);
  
  // Calculate pitch (up/down tilt)
  // Angle between hand direction and xy-plane
  const xyPlaneNormal = { x: 0, y: 0, z: 1 };
  const handDirectionXY = { x: normalizedHandDirection.x, y: normalizedHandDirection.y, z: 0 };
  const normalizedHandDirectionXY = normalizeVector(handDirectionXY);
  const pitch = angleBetweenVectors(normalizedHandDirection, normalizedHandDirectionXY);
  // Adjust sign based on direction
  const pitchDegrees = (normalizedHandDirection.z < 0) ? -pitch * (180 / Math.PI) : pitch * (180 / Math.PI);
  
  // Calculate yaw (left/right rotation)
  // Angle between hand direction projected on xy-plane and x-axis
  const xAxis = { x: 1, y: 0, z: 0 };
  const yaw = angleBetweenVectors(normalizedHandDirectionXY, xAxis);
  // Adjust sign based on direction
  const yawDegrees = (normalizedHandDirectionXY.y < 0) ? -yaw * (180 / Math.PI) : yaw * (180 / Math.PI);
  
  // Calculate roll (twist)
  // We need to create a reference vector perpendicular to hand direction
  const upVector = { x: 0, y: 1, z: 0 }; // Use y-axis as up vector
  const referenceVector = crossProduct(normalizedHandDirection, upVector);
  const normalizedReferenceVector = normalizeVector(referenceVector);
  
  // Project finger direction onto the plane perpendicular to hand direction
  const fingerProjection = subtractVectorProjection(normalizedFingerDirection, normalizedHandDirection);
  const normalizedFingerProjection = normalizeVector(fingerProjection);
  
  // Calculate roll angle
  const roll = angleBetweenVectors(normalizedFingerProjection, normalizedReferenceVector);
  // Adjust sign based on direction
  const rollDegrees = roll * (180 / Math.PI);
  
  return {
    pitch: pitchDegrees,
    yaw: yawDegrees,
    roll: rollDegrees,
    handDirection: normalizedHandDirection,
    fingerDirection: normalizedFingerDirection
  };
};

/**
 * Calculate depth information from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Depth information
 */
const calculateDepthInformation = (landmarks) => {
  // Get key landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  const middleMCP = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
  
  // Calculate average z-coordinate of wrist landmarks
  const avgWristZ = (wrist.z + thumbCMC.z + pinkyMCP.z) / 3;
  
  // Calculate z-range (depth variation across the hand)
  const zValues = landmarks.map(landmark => landmark.z);
  const minZ = Math.min(...zValues);
  const maxZ = Math.max(...zValues);
  const zRange = maxZ - minZ;
  
  // Calculate depth gradient (how much depth changes across the wrist)
  const depthGradient = {
    x: (thumbCMC.z - pinkyMCP.z) / (thumbCMC.x - pinkyMCP.x),
    y: (middleMCP.z - wrist.z) / (middleMCP.y - wrist.y)
  };
  
  return {
    avgWristZ,
    zRange,
    minZ,
    maxZ,
    depthGradient
  };
};

/**
 * Calculate perspective transformation matrix for 2.5D rendering
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} wristPlane - Wrist plane data
 * @returns {Array} - 3x3 perspective transformation matrix
 */
const calculatePerspectiveMatrix = (landmarks, wristPlane) => {
  // Get key wrist landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  
  // Define source points (rectangle in 3D space)
  const sourcePoints = [
    { x: wrist.x, y: wrist.y },
    { x: thumbCMC.x, y: thumbCMC.y },
    { x: pinkyMCP.x, y: pinkyMCP.y },
    { x: indexMCP.x, y: indexMCP.y }
  ];
  
  // Define destination points (rectangle in 2D space)
  // This would typically be the corners of the watch image
  const destPoints = [
    { x: 0, y: 1 },
    { x: 0, y: 0 },
    { x: 1, y: 0 },
    { x: 1, y: 1 }
  ];
  
  // Calculate homography matrix (perspective transformation)
  // This is a simplified version - in practice, use a proper homography algorithm
  // or a library like OpenCV.js
  
  // For now, return an identity matrix as placeholder
  // In a real implementation, this would be calculated from the source and destination points
  return [
    [1, 0, 0],
    [0, 1, 0],
    [0, 0, 1]
  ];
};

/**
 * Calculate cross product of two vectors
 * @param {Object} v1 - First vector {x, y, z}
 * @param {Object} v2 - Second vector {x, y, z}
 * @returns {Object} - Cross product vector
 */
const crossProduct = (v1, v2) => {
  return {
    x: v1.y * v2.z - v1.z * v2.y,
    y: v1.z * v2.x - v1.x * v2.z,
    z: v1.x * v2.y - v1.y * v2.x
  };
};

/**
 * Normalize a vector
 * @param {Object} vector - Vector {x, y, z}
 * @returns {Object} - Normalized vector
 */
const normalizeVector = (vector) => {
  const length = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
  if (length === 0) {
    return { x: 0, y: 0, z: 0 };
  }
  return {
    x: vector.x / length,
    y: vector.y / length,
    z: vector.z / length
  };
};

/**
 * Calculate angle between two vectors in radians
 * @param {Object} v1 - First vector {x, y, z}
 * @param {Object} v2 - Second vector {x, y, z}
 * @returns {number} - Angle in radians
 */
const angleBetweenVectors = (v1, v2) => {
  const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
  const len1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
  const len2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y + v2.z * v2.z);
  
  // Ensure we don't get NaN due to floating point errors
  const cosAngle = Math.max(-1, Math.min(1, dot / (len1 * len2)));
  return Math.acos(cosAngle);
};

/**
 * Calculate 3D distance between two points
 * @param {Object} p1 - First point {x, y, z}
 * @param {Object} p2 - Second point {x, y, z}
 * @returns {number} - Distance
 */
const distance3D = (p1, p2) => {
  return Math.sqrt(
    Math.pow(p2.x - p1.x, 2) +
    Math.pow(p2.y - p1.y, 2) +
    Math.pow(p2.z - p1.z, 2)
  );
};

/**
 * Subtract the projection of v1 onto v2 from v1
 * @param {Object} v1 - Vector to project from {x, y, z}
 * @param {Object} v2 - Vector to project onto {x, y, z}
 * @returns {Object} - Result vector
 */
const subtractVectorProjection = (v1, v2) => {
  const dot = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
  const len2 = v2.x * v2.x + v2.y * v2.y + v2.z * v2.z;
  const scale = dot / len2;
  
  return {
    x: v1.x - scale * v2.x,
    y: v1.y - scale * v2.y,
    z: v1.z - scale * v2.z
  };
};

/**
 * Convert 3D hand pose to 2D watch transformation parameters
 * @param {Object} handPose - 2.5D hand pose data
 * @param {Object} watchDimensions - Watch dimensions
 * @returns {Object} - 2D transformation parameters for watch
 */
export const convertPoseTo2DTransform = (handPose, watchDimensions) => {
  if (!handPose) return null;
  
  const { wristPlane, wristDimensions, handOrientation, depthInfo } = handPose;
  
  // Calculate scale factor based on wrist width and watch width
  const scaleX = wristDimensions.width / watchDimensions.width;
  const scaleY = wristDimensions.height / watchDimensions.height;
  const scale = (scaleX + scaleY) / 2;
  
  // Calculate rotation based on hand orientation
  const rotation = {
    x: handOrientation.pitch,
    y: handOrientation.yaw,
    z: handOrientation.roll
  };
  
  // Calculate translation to position watch on wrist
  const translation = {
    x: wristPlane.center.x,
    y: wristPlane.center.y,
    z: wristPlane.center.z
  };
  
  // Calculate perspective distortion based on wrist angle
  const perspective = {
    angle: wristPlane.angle,
    depthGradient: depthInfo.depthGradient
  };
  
  return {
    scale,
    rotation,
    translation,
    perspective
  };
};

export default {
  calculate2_5DHandPose,
  convertPoseTo2DTransform
};
