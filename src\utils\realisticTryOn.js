/**
 * Realistic Try-On Integration Module
 * 
 * This module integrates all the realistic try-on components:
 * - MediaPipe hand detection
 * - 2.5D hand pose estimation
 * - Wrist segmentation
 * - Image warping
 * - Real measurement scaling
 * - Posture-aware transforms
 */

import * as handDetection from './handDetection';
import * as handPoseEstimation from './handPoseEstimation';
import * as wristSegmentation from './wristSegmentation';
import * as imageWarping from './imageWarping';
import * as realMeasurementScaling from './realMeasurementScaling';
import * as postureTransforms from './postureTransforms';

// State variables
let isInitialized = false;
let lastHandData = null;
let lastHandPose = null;
let lastRealWorldMeasurements = null;
let processingFrame = false;
let frameSkipCounter = 0;
let frameSkipThreshold = 1; // Process every other frame by default
let debugMode = false;

// Performance metrics
const performanceMetrics = {
  handDetectionTime: 0,
  poseEstimationTime: 0,
  segmentationTime: 0,
  warpingTime: 0,
  scalingTime: 0,
  transformTime: 0,
  totalProcessingTime: 0,
  framesProcessed: 0,
  frameRate: 0,
  lastFrameTime: 0
};

/**
 * Initialize the realistic try-on system
 * @param {HTMLVideoElement} videoElement - Video element for camera feed
 * @param {HTMLCanvasElement} canvasElement - Canvas element for visualization
 * @param {Function} onHandDataUpdate - Callback for hand data updates
 * @param {Object} options - Initialization options
 * @returns {Promise<boolean>} - Whether initialization was successful
 */
export const initRealisticTryOn = async (
  videoElement,
  canvasElement,
  onHandDataUpdate,
  options = {}
) => {
  const {
    enableDebug = false,
    targetFrameRate = 30,
    enableOpenCV = true
  } = options;

  try {
    // Set debug mode
    debugMode = enableDebug;
    log('Initializing realistic try-on system...');

    // Check if MediaPipe is available
    if (!window.Hands) {
      console.error('❌ MediaPipe Hands not found. Make sure MediaPipe scripts are loaded.');
      return false;
    }

    if (!window.Camera) {
      console.warn('⚠️ MediaPipe Camera not found. Will use fallback implementation.');
    } else {
      log('✅ MediaPipe Camera found');
    }

    log('✅ MediaPipe Hands library found');

    // Calculate frame skip threshold based on target frame rate
    // Assuming camera runs at 60fps, we need to skip frames to achieve target rate
    frameSkipThreshold = Math.max(1, Math.round(60 / targetFrameRate) - 1);
    log(`Frame skip threshold set to ${frameSkipThreshold} for target ${targetFrameRate}fps`);

    // Initialize hand detection
    const handDetectionInitialized = await handDetection.initHandDetection(
      videoElement,
      canvasElement,
      (handData) => processHandData(handData, onHandDataUpdate)
    );

    if (!handDetectionInitialized) {
      throw new Error('Failed to initialize hand detection');
    }

    // Initialize OpenCV if enabled
    if (enableOpenCV) {
      const openCVInitialized = await imageWarping.initOpenCV();
      if (!openCVInitialized) {
        console.warn('Failed to initialize OpenCV. Some features will be disabled.');
      }
    }

    isInitialized = true;
    log('Realistic try-on system initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing realistic try-on system:', error);
    return false;
  }
};

/**
 * Process hand data from MediaPipe
 * @param {Object} handData - Hand data from MediaPipe
 * @param {Function} onHandDataUpdate - Callback for hand data updates
 */
const processHandData = async (handData, onHandDataUpdate) => {
  if (!isInitialized || processingFrame) return;

  // Skip frames for performance
  if (frameSkipCounter < frameSkipThreshold) {
    frameSkipCounter++;
    return;
  }
  frameSkipCounter = 0;

  processingFrame = true;
  const startTime = performance.now();

  try {
    // Store hand data
    lastHandData = handData;

    if (!handData.detected) {
      // No hand detected, update callback with null data
      if (onHandDataUpdate) {
        onHandDataUpdate({
          handData,
          handPose: null,
          realWorldMeasurements: null,
          segmentationMask: null,
          transforms: null,
          performanceMetrics: debugMode ? performanceMetrics : null
        });
      }
      processingFrame = false;
      return;
    }

    // Calculate 2.5D hand pose
    const poseStartTime = performance.now();
    const handPose = handPoseEstimation.calculate2_5DHandPose(handData.landmarks);
    lastHandPose = handPose;
    performanceMetrics.poseEstimationTime = performance.now() - poseStartTime;

    // Calculate real-world measurements
    const scalingStartTime = performance.now();
    const realWorldMeasurements = realMeasurementScaling.calculateRealWorldMeasurements(
      handData.landmarks
    );
    lastRealWorldMeasurements = realWorldMeasurements;
    performanceMetrics.scalingTime = performance.now() - scalingStartTime;

    // Calculate posture-aware transforms
    const transformStartTime = performance.now();
    const transforms = postureTransforms.calculatePostureTransforms(
      handData.landmarks,
      handPose
    );
    performanceMetrics.transformTime = performance.now() - transformStartTime;

    // Update performance metrics
    performanceMetrics.totalProcessingTime = performance.now() - startTime;
    performanceMetrics.framesProcessed++;
    
    const now = performance.now();
    if (performanceMetrics.lastFrameTime) {
      const frameTime = now - performanceMetrics.lastFrameTime;
      performanceMetrics.frameRate = 1000 / frameTime;
    }
    performanceMetrics.lastFrameTime = now;

    // Call the update callback
    if (onHandDataUpdate) {
      onHandDataUpdate({
        handData,
        handPose,
        realWorldMeasurements,
        transforms,
        performanceMetrics: debugMode ? performanceMetrics : null
      });
    }
  } catch (error) {
    console.error('Error processing hand data:', error);
  } finally {
    processingFrame = false;
  }
};

/**
 * Create a segmentation mask for the wrist
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @param {Object} options - Segmentation options
 * @returns {ImageData} - Segmentation mask
 */
export const createWristSegmentationMask = (width, height, options = {}) => {
  if (!lastHandData || !lastHandData.detected) {
    return null;
  }

  const segmentationStartTime = performance.now();
  const mask = wristSegmentation.createWristMask(
    lastHandData.landmarks,
    width,
    height,
    options
  );
  performanceMetrics.segmentationTime = performance.now() - segmentationStartTime;

  return mask;
};

/**
 * Create a warped watch image based on hand pose
 * @param {HTMLImageElement|HTMLCanvasElement} watchImage - Watch image
 * @param {Object} options - Warping options
 * @returns {Promise<HTMLCanvasElement>} - Warped watch image
 */
export const createWarpedWatchImage = async (watchImage, options = {}) => {
  if (!lastHandPose) {
    return null;
  }

  const warpingStartTime = performance.now();
  const warpedImage = await imageWarping.createEnhancedWatchImage(
    watchImage,
    lastHandPose,
    options
  );
  performanceMetrics.warpingTime = performance.now() - warpingStartTime;

  return warpedImage;
};

/**
 * Calculate optimal watch size for the detected wrist
 * @param {Object} options - Sizing options
 * @returns {Object} - Recommended watch sizing
 */
export const getOptimalWatchSize = (options = {}) => {
  if (!lastRealWorldMeasurements) {
    return null;
  }

  return realMeasurementScaling.calculateOptimalWatchSize(
    lastRealWorldMeasurements,
    options
  );
};

/**
 * Calculate watch scaling based on real measurements
 * @param {Object} watchSpecs - Watch specifications
 * @param {Object} displayDimensions - Display dimensions
 * @returns {Object} - Scaling parameters
 */
export const getWatchScaling = (watchSpecs, displayDimensions) => {
  if (!lastRealWorldMeasurements) {
    return { scaleX: 1, scaleY: 1, offsetX: 0, offsetY: 0 };
  }

  return realMeasurementScaling.calculateWatchScaling(
    watchSpecs,
    lastRealWorldMeasurements,
    displayDimensions
  );
};

/**
 * Get CSS styles for posture-aware transformations
 * @param {Object} options - Style options
 * @returns {Object} - CSS style object
 */
export const getPostureTransformStyles = (options = {}) => {
  if (!lastHandPose) {
    return {};
  }

  const transforms = postureTransforms.calculatePostureTransforms(
    lastHandData.landmarks,
    lastHandPose
  );

  return postureTransforms.applyPostureTransformsToStyle(transforms, options);
};

/**
 * Get CSS filter styles based on hand pose
 * @param {Object} options - Filter options
 * @returns {Object} - CSS style object with filters
 */
export const getPostureAwareFilters = (options = {}) => {
  if (!lastHandPose) {
    return {};
  }

  return postureTransforms.createPostureAwareFilters(lastHandPose, options);
};

/**
 * Process a single image for hand detection
 * @param {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement} image - Image to process
 * @returns {Promise<Object>} - Processing results
 */
export const processImage = async (image) => {
  if (!isInitialized) {
    throw new Error('Realistic try-on system not initialized');
  }

  try {
    // Detect hands in the image
    const handData = await handDetection.detectHandsInImage(image);
    
    if (!handData || !handData.detected) {
      return {
        handData,
        handPose: null,
        realWorldMeasurements: null,
        transforms: null
      };
    }

    // Calculate 2.5D hand pose
    const handPose = handPoseEstimation.calculate2_5DHandPose(handData.landmarks);
    
    // Calculate real-world measurements
    const realWorldMeasurements = realMeasurementScaling.calculateRealWorldMeasurements(
      handData.landmarks
    );
    
    // Calculate posture-aware transforms
    const transforms = postureTransforms.calculatePostureTransforms(
      handData.landmarks,
      handPose
    );

    return {
      handData,
      handPose,
      realWorldMeasurements,
      transforms
    };
  } catch (error) {
    console.error('Error processing image:', error);
    return null;
  }
};

/**
 * Stop the realistic try-on system and release resources
 */
export const stopRealisticTryOn = async () => {
  if (!isInitialized) return;

  try {
    await handDetection.stopHandDetection();
    isInitialized = false;
    lastHandData = null;
    lastHandPose = null;
    lastRealWorldMeasurements = null;
    log('Realistic try-on system stopped');
  } catch (error) {
    console.error('Error stopping realistic try-on system:', error);
  }
};

/**
 * Set debug mode
 * @param {boolean} enabled - Whether to enable debug mode
 */
export const setDebugMode = (enabled) => {
  debugMode = enabled;
};

/**
 * Get current performance metrics
 * @returns {Object} - Performance metrics
 */
export const getPerformanceMetrics = () => {
  return { ...performanceMetrics };
};

/**
 * Log debug message
 * @param {string} message - Debug message
 */
const log = (message) => {
  if (debugMode) {
    console.log(`[RealisticTryOn] ${message}`);
  }
};

export default {
  initRealisticTryOn,
  createWristSegmentationMask,
  createWarpedWatchImage,
  getOptimalWatchSize,
  getWatchScaling,
  getPostureTransformStyles,
  getPostureAwareFilters,
  processImage,
  stopRealisticTryOn,
  setDebugMode,
  getPerformanceMetrics
};
