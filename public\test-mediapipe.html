<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaPipe Test</title>
</head>
<body>
    <h1>MediaPipe Test Page</h1>
    <div id="status">Loading...</div>
    
    <!-- MediaPipe Scripts -->
    <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils@0.3.1620248257/camera_utils.js"></script>
    <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils@0.6.1620248257/control_utils.js"></script>
    <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils@0.3.1620248257/drawing_utils.js"></script>
    <script crossorigin="anonymous" src="https://cdn.jsdelivr.net/npm/@mediapipe/hands@0.4.1646424915/hands.js"></script>
    
    <script>
        function checkMediaPipe() {
            const status = document.getElementById('status');
            let statusText = '';
            
            statusText += 'MediaPipe Hands: ' + (window.Hands ? '✅ Available' : '❌ Not found') + '<br>';
            statusText += 'MediaPipe Camera: ' + (window.Camera ? '✅ Available' : '❌ Not found') + '<br>';
            statusText += 'MediaPipe drawConnectors: ' + (window.drawConnectors ? '✅ Available' : '❌ Not found') + '<br>';
            statusText += 'MediaPipe drawLandmarks: ' + (window.drawLandmarks ? '✅ Available' : '❌ Not found') + '<br>';
            
            status.innerHTML = statusText;
            
            console.log('MediaPipe Test Results:');
            console.log('Hands:', window.Hands);
            console.log('Camera:', window.Camera);
            console.log('drawConnectors:', window.drawConnectors);
            console.log('drawLandmarks:', window.drawLandmarks);
        }
        
        // Check immediately
        checkMediaPipe();
        
        // Check again after a delay to ensure scripts are loaded
        setTimeout(checkMediaPipe, 2000);
    </script>
</body>
</html>
