/**
 * MediaPipe Hand Detection Module
 * 
 * This module provides advanced hand detection and landmark tracking using MediaPipe Hands.
 * It detects 21 hand landmarks, wrist position, and hand orientation for realistic watch try-on.
 */

// Hand landmark indices for reference
const HAND_LANDMARKS = {
  WRIST: 0,
  THUMB_CMC: 1,
  THUMB_MCP: 2,
  THUMB_IP: 3,
  THUMB_TIP: 4,
  INDEX_FINGER_MCP: 5,
  INDEX_FINGER_PIP: 6,
  INDEX_FINGER_DIP: 7,
  INDEX_FINGER_TIP: 8,
  MIDDLE_FINGER_MCP: 9,
  MIDDLE_FINGER_PIP: 10,
  MIDDLE_FINGER_DIP: 11,
  MIDDLE_FINGER_TIP: 12,
  RING_FINGER_MCP: 13,
  RING_FINGER_PIP: 14,
  RING_FINGER_DIP: 15,
  RING_FINGER_TIP: 16,
  PINKY_MCP: 17,
  PINKY_PIP: 18,
  PINKY_DIP: 19,
  PINKY_TIP: 20,
};

// Wrist landmarks for watch placement
const WRIST_LANDMARKS = [
  HAND_LANDMARKS.WRIST,
  HAND_LANDMARKS.THUMB_CMC,
  HAND_LANDMARKS.PINKY_MCP
];

// MediaPipe Hands detector instance
let handsDetector = null;
let camera = null;
let isInitialized = false;
let lastResults = null;
let onResultsCallback = null;
let canvasElement = null;
let canvasCtx = null;
let videoElement = null;

/**
 * Initialize the MediaPipe Hands detector
 * @param {HTMLVideoElement} video - Video element for camera feed
 * @param {HTMLCanvasElement} canvas - Canvas element for visualization (optional)
 * @param {Function} callback - Callback function for detection results
 * @returns {Promise<boolean>} - Whether initialization was successful
 */
export const initHandDetection = async (video, canvas, callback) => {
  try {
    // Check if MediaPipe is available
    if (!window.Hands) {
      console.error('MediaPipe Hands not found. Make sure to include the MediaPipe scripts.');
      return false;
    }

    // Store references
    videoElement = video;
    canvasElement = canvas;
    if (canvas) {
      canvasCtx = canvas.getContext('2d');
    }
    onResultsCallback = callback;

    // Initialize MediaPipe Hands
    handsDetector = new window.Hands({
      locateFile: (file) => {
        return `https://cdn.jsdelivr.net/npm/@mediapipe/hands@0.4.1646424915/${file}`;
      }
    });

    // Configure for better performance and accuracy
    await handsDetector.setOptions({
      maxNumHands: 1, // Only detect one hand for watch try-on
      modelComplexity: 1, // 0, 1, or 2 - higher is more accurate but slower
      minDetectionConfidence: 0.5,
      minTrackingConfidence: 0.5,
      selfieMode: true, // Mirror the input for selfie view
    });

    // Set up the callback
    handsDetector.onResults(onResults);

    // Initialize camera processing without MediaPipe Camera utility
    if (video) {
      console.log('Setting up direct video processing (no MediaPipe Camera dependency)');

      // Create a simple camera implementation that processes video frames directly
      let isProcessing = false;
      let animationFrameId = null;

      const processFrame = async () => {
        if (!handsDetector || isProcessing) {
          animationFrameId = requestAnimationFrame(processFrame);
          return;
        }

        // Check if video is ready
        if (video.readyState === 4 && video.videoWidth > 0 && video.videoHeight > 0) {
          isProcessing = true;
          try {
            await handsDetector.send({ image: video });
          } catch (error) {
            console.error('Error processing frame:', error);
          }
          isProcessing = false;
        }

        animationFrameId = requestAnimationFrame(processFrame);
      };

      // Create camera object
      camera = {
        start: async () => {
          console.log('Starting direct video processing');
          animationFrameId = requestAnimationFrame(processFrame);
          return true;
        },
        stop: () => {
          console.log('Stopping direct video processing');
          if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
          }
          return true;
        }
      };

      // Start the camera processing
      await camera.start();
      console.log('Direct video processing started successfully');
    }

    isInitialized = true;
    console.log('MediaPipe Hands initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing MediaPipe Hands:', error);
    return false;
  }
};

/**
 * Process results from MediaPipe Hands
 * @param {Object} results - Results from MediaPipe Hands
 */
const onResults = (results) => {
  lastResults = results;

  // Draw landmarks on canvas if available
  if (canvasElement && canvasCtx) {
    drawLandmarks(results);
  }

  // Process the results to extract useful information
  const processedResults = processHandResults(results);

  // Call the callback with processed results
  if (onResultsCallback) {
    onResultsCallback(processedResults);
  }
};

/**
 * Process hand detection results to extract useful information
 * @param {Object} results - Results from MediaPipe Hands
 * @returns {Object} - Processed hand data
 */
const processHandResults = (results) => {
  if (!results || !results.multiHandLandmarks || results.multiHandLandmarks.length === 0) {
    return {
      detected: false,
      landmarks: null,
      handedness: null,
      wristPosition: null,
      wristWidth: null,
      wristAngle: null,
      handPose: null,
      confidence: 0
    };
  }

  const landmarks = results.multiHandLandmarks[0]; // Get the first hand
  const handedness = results.multiHandedness[0].label; // "Left" or "Right"
  const confidence = results.multiHandedness[0].score;
  const isRightHand = handedness === 'Right';

  // Calculate wrist position (center point between landmarks)
  const wristPosition = calculateWristPosition(landmarks);

  // Calculate wrist width (distance between thumb base and pinky base)
  const wristWidth = calculateWristWidth(landmarks);

  // Calculate wrist angle (angle of the wrist line)
  const wristAngle = calculateWristAngle(landmarks);

  // Determine hand pose (open, closed, etc.)
  const handPose = determineHandPose(landmarks);

  // Calculate 3D rotation of the hand
  const rotation = calculate3DRotation(landmarks);

  return {
    detected: true,
    landmarks,
    handedness,
    isRightHand,
    wristPosition,
    wristWidth,
    wristAngle,
    handPose,
    rotation,
    confidence
  };
};

/**
 * Calculate the wrist position from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Wrist position {x, y, z}
 */
const calculateWristPosition = (landmarks) => {
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate the center point of the wrist
  return {
    x: (wrist.x + thumbCMC.x + pinkyMCP.x) / 3,
    y: (wrist.y + thumbCMC.y + pinkyMCP.y) / 3,
    z: (wrist.z + thumbCMC.z + pinkyMCP.z) / 3
  };
};

/**
 * Calculate the wrist width from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {number} - Wrist width (normalized)
 */
const calculateWristWidth = (landmarks) => {
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate Euclidean distance between thumb base and pinky base
  const distance = Math.sqrt(
    Math.pow(thumbCMC.x - pinkyMCP.x, 2) +
    Math.pow(thumbCMC.y - pinkyMCP.y, 2) +
    Math.pow(thumbCMC.z - pinkyMCP.z, 2)
  );

  return distance;
};

/**
 * Calculate the wrist angle from landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {number} - Wrist angle in degrees
 */
const calculateWristAngle = (landmarks) => {
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate angle between thumb base and pinky base
  const angle = Math.atan2(
    pinkyMCP.y - thumbCMC.y,
    pinkyMCP.x - thumbCMC.x
  ) * (180 / Math.PI);

  return angle;
};

/**
 * Calculate 3D rotation of the hand
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - 3D rotation {pitch, yaw, roll}
 */
const calculate3DRotation = (landmarks) => {
  // Get key landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const middleMCP = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
  const middleTip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP];
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate vectors
  const palmNormal = calculatePalmNormal(landmarks);
  
  // Calculate pitch (up/down tilt)
  const pitch = Math.atan2(palmNormal.y, palmNormal.z) * (180 / Math.PI);
  
  // Calculate yaw (left/right rotation)
  const yaw = Math.atan2(palmNormal.x, palmNormal.z) * (180 / Math.PI);
  
  // Calculate roll (twist)
  const wristToMiddle = {
    x: middleMCP.x - wrist.x,
    y: middleMCP.y - wrist.y
  };
  const roll = Math.atan2(wristToMiddle.y, wristToMiddle.x) * (180 / Math.PI);

  return { pitch, yaw, roll };
};

/**
 * Calculate the palm normal vector (perpendicular to palm)
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Object} - Normal vector {x, y, z}
 */
const calculatePalmNormal = (landmarks) => {
  // Get key landmarks to define the palm plane
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate vectors along the palm
  const vector1 = {
    x: indexMCP.x - wrist.x,
    y: indexMCP.y - wrist.y,
    z: indexMCP.z - wrist.z
  };

  const vector2 = {
    x: pinkyMCP.x - wrist.x,
    y: pinkyMCP.y - wrist.y,
    z: pinkyMCP.z - wrist.z
  };

  // Calculate cross product to get normal vector
  const normal = {
    x: vector1.y * vector2.z - vector1.z * vector2.y,
    y: vector1.z * vector2.x - vector1.x * vector2.z,
    z: vector1.x * vector2.y - vector1.y * vector2.x
  };

  // Normalize the vector
  const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
  return {
    x: normal.x / length,
    y: normal.y / length,
    z: normal.z / length
  };
};

/**
 * Determine the hand pose based on finger positions
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {string} - Hand pose (open, closed, pointing, etc.)
 */
const determineHandPose = (landmarks) => {
  // Calculate finger curl for each finger
  const thumbCurl = calculateFingerCurl(landmarks, 'thumb');
  const indexCurl = calculateFingerCurl(landmarks, 'index');
  const middleCurl = calculateFingerCurl(landmarks, 'middle');
  const ringCurl = calculateFingerCurl(landmarks, 'ring');
  const pinkyCurl = calculateFingerCurl(landmarks, 'pinky');

  // Determine hand pose based on finger curls
  if (thumbCurl < 0.5 && indexCurl < 0.5 && middleCurl < 0.5 && ringCurl < 0.5 && pinkyCurl < 0.5) {
    return 'open';
  } else if (thumbCurl > 0.5 && indexCurl > 0.5 && middleCurl > 0.5 && ringCurl > 0.5 && pinkyCurl > 0.5) {
    return 'closed';
  } else if (indexCurl < 0.5 && middleCurl > 0.5 && ringCurl > 0.5 && pinkyCurl > 0.5) {
    return 'pointing';
  } else {
    return 'other';
  }
};

/**
 * Calculate the curl of a finger (how bent it is)
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {string} finger - Finger name (thumb, index, middle, ring, pinky)
 * @returns {number} - Curl value between 0 (straight) and 1 (fully curled)
 */
const calculateFingerCurl = (landmarks, finger) => {
  let mcp, pip, dip, tip;

  switch (finger) {
    case 'thumb':
      mcp = landmarks[HAND_LANDMARKS.THUMB_CMC];
      pip = landmarks[HAND_LANDMARKS.THUMB_MCP];
      dip = landmarks[HAND_LANDMARKS.THUMB_IP];
      tip = landmarks[HAND_LANDMARKS.THUMB_TIP];
      break;
    case 'index':
      mcp = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
      pip = landmarks[HAND_LANDMARKS.INDEX_FINGER_PIP];
      dip = landmarks[HAND_LANDMARKS.INDEX_FINGER_DIP];
      tip = landmarks[HAND_LANDMARKS.INDEX_FINGER_TIP];
      break;
    case 'middle':
      mcp = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
      pip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_PIP];
      dip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_DIP];
      tip = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP];
      break;
    case 'ring':
      mcp = landmarks[HAND_LANDMARKS.RING_FINGER_MCP];
      pip = landmarks[HAND_LANDMARKS.RING_FINGER_PIP];
      dip = landmarks[HAND_LANDMARKS.RING_FINGER_DIP];
      tip = landmarks[HAND_LANDMARKS.RING_FINGER_TIP];
      break;
    case 'pinky':
      mcp = landmarks[HAND_LANDMARKS.PINKY_MCP];
      pip = landmarks[HAND_LANDMARKS.PINKY_PIP];
      dip = landmarks[HAND_LANDMARKS.PINKY_DIP];
      tip = landmarks[HAND_LANDMARKS.PINKY_TIP];
      break;
    default:
      return 0;
  }

  // Calculate vectors
  const mcpToPip = {
    x: pip.x - mcp.x,
    y: pip.y - mcp.y,
    z: pip.z - mcp.z
  };
  const pipToDip = {
    x: dip.x - pip.x,
    y: dip.y - pip.y,
    z: dip.z - pip.z
  };
  const dipToTip = {
    x: tip.x - dip.x,
    y: tip.y - dip.y,
    z: tip.z - dip.z
  };

  // Normalize vectors
  const normalizeMcpToPip = normalizeVector(mcpToPip);
  const normalizePipToDip = normalizeVector(pipToDip);
  const normalizeDipToTip = normalizeVector(dipToTip);

  // Calculate dot products to get angles
  const angle1 = Math.acos(dotProduct(normalizeMcpToPip, normalizePipToDip));
  const angle2 = Math.acos(dotProduct(normalizePipToDip, normalizeDipToTip));

  // Convert to degrees
  const angle1Deg = angle1 * (180 / Math.PI);
  const angle2Deg = angle2 * (180 / Math.PI);

  // Calculate curl (0 = straight, 1 = fully curled)
  // Max angle is about 180 degrees for a fully curled finger
  const curl = (angle1Deg + angle2Deg) / 180;

  return Math.min(1, Math.max(0, curl));
};

/**
 * Normalize a vector
 * @param {Object} vector - Vector {x, y, z}
 * @returns {Object} - Normalized vector
 */
const normalizeVector = (vector) => {
  const length = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
  return {
    x: vector.x / length,
    y: vector.y / length,
    z: vector.z / length
  };
};

/**
 * Calculate dot product of two vectors
 * @param {Object} v1 - First vector {x, y, z}
 * @param {Object} v2 - Second vector {x, y, z}
 * @returns {number} - Dot product
 */
const dotProduct = (v1, v2) => {
  return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
};

/**
 * Draw hand landmarks on canvas
 * @param {Object} results - Results from MediaPipe Hands
 */
const drawLandmarks = (results) => {
  if (!canvasCtx || !canvasElement) return;

  canvasCtx.save();
  canvasCtx.clearRect(0, 0, canvasElement.width, canvasElement.height);
  
  // Draw the camera feed
  if (videoElement) {
    canvasCtx.drawImage(
      videoElement, 0, 0, canvasElement.width, canvasElement.height);
  }

  // Draw hand landmarks
  if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
    for (const landmarks of results.multiHandLandmarks) {
      window.drawConnectors(canvasCtx, landmarks, window.HAND_CONNECTIONS,
        { color: '#00FF00', lineWidth: 5 });
      window.drawLandmarks(canvasCtx, landmarks, {
        color: '#FF0000',
        lineWidth: 2,
        radius: 4
      });
      
      // Highlight wrist landmarks
      const wristLandmarks = [
        landmarks[HAND_LANDMARKS.WRIST],
        landmarks[HAND_LANDMARKS.THUMB_CMC],
        landmarks[HAND_LANDMARKS.PINKY_MCP]
      ];
      
      window.drawLandmarks(canvasCtx, wristLandmarks, {
        color: '#0000FF',
        fillColor: '#0000FF',
        lineWidth: 2,
        radius: 8
      });
    }
  }

  canvasCtx.restore();
};

/**
 * Process a single image for hand detection
 * @param {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement} image - Image to process
 * @returns {Promise<Object>} - Processed hand data
 */
export const detectHandsInImage = async (image) => {
  if (!handsDetector) {
    console.error('Hand detector not initialized');
    return null;
  }

  try {
    // Process the image
    await handsDetector.send({ image });
    
    // Return the last results
    return processHandResults(lastResults);
  } catch (error) {
    console.error('Error detecting hands in image:', error);
    return null;
  }
};

/**
 * Stop hand detection and release resources
 */
export const stopHandDetection = async () => {
  if (camera) {
    await camera.stop();
    camera = null;
  }
  
  if (handsDetector) {
    await handsDetector.close();
    handsDetector = null;
  }
  
  isInitialized = false;
  lastResults = null;
  onResultsCallback = null;
  canvasElement = null;
  canvasCtx = null;
  videoElement = null;
  
  console.log('Hand detection stopped');
};

export { HAND_LANDMARKS };

export default {
  initHandDetection,
  detectHandsInImage,
  stopHandDetection,
  HAND_LANDMARKS
};
