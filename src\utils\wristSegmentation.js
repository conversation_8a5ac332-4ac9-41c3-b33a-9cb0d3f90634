/**
 * Advanced Wrist Segmentation Module
 * 
 * This module provides intelligent wrist area segmentation using MediaPipe landmarks
 * to create realistic occlusion masks for watch placement.
 */

import { HAND_LANDMARKS } from './handDetection';

/**
 * Create a wrist segmentation mask from MediaPipe landmarks
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @param {Object} options - Segmentation options
 * @returns {ImageData} - Segmentation mask
 */
export const createWristMask = (landmarks, width, height, options = {}) => {
  const {
    maskType = 'ellipse', // 'ellipse', 'polygon', 'gradient'
    featherRadius = 10,
    expandFactor = 1.2,
    includeFingers = false
  } = options;

  // Create canvas for mask
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, width, height);

  if (!landmarks || landmarks.length === 0) {
    return ctx.getImageData(0, 0, width, height);
  }

  // Get wrist landmarks
  const wristLandmarks = getWristLandmarks(landmarks);
  
  // Convert normalized coordinates to pixel coordinates
  const pixelLandmarks = wristLandmarks.map(landmark => ({
    x: landmark.x * width,
    y: landmark.y * height,
    z: landmark.z
  }));

  // Create mask based on type
  switch (maskType) {
    case 'ellipse':
      createEllipseMask(ctx, pixelLandmarks, expandFactor, featherRadius);
      break;
    case 'polygon':
      createPolygonMask(ctx, pixelLandmarks, expandFactor, featherRadius);
      break;
    case 'gradient':
      createGradientMask(ctx, pixelLandmarks, expandFactor, featherRadius);
      break;
    default:
      createEllipseMask(ctx, pixelLandmarks, expandFactor, featherRadius);
  }

  // Add finger occlusion if requested
  if (includeFingers) {
    addFingerOcclusion(ctx, landmarks, width, height);
  }

  return ctx.getImageData(0, 0, width, height);
};

/**
 * Get wrist landmarks for segmentation
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @returns {Array} - Wrist landmarks
 */
const getWristLandmarks = (landmarks) => {
  return [
    landmarks[HAND_LANDMARKS.WRIST],
    landmarks[HAND_LANDMARKS.THUMB_CMC],
    landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP],
    landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP],
    landmarks[HAND_LANDMARKS.RING_FINGER_MCP],
    landmarks[HAND_LANDMARKS.PINKY_MCP]
  ];
};

/**
 * Create an elliptical mask around the wrist
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Array} landmarks - Pixel coordinates of wrist landmarks
 * @param {number} expandFactor - Factor to expand the ellipse
 * @param {number} featherRadius - Feathering radius for soft edges
 */
const createEllipseMask = (ctx, landmarks, expandFactor, featherRadius) => {
  // Calculate wrist center
  const center = calculateWristCenter(landmarks);
  
  // Calculate wrist dimensions
  const dimensions = calculateWristDimensions(landmarks);
  
  // Expand dimensions
  const radiusX = (dimensions.width / 2) * expandFactor;
  const radiusY = (dimensions.height / 2) * expandFactor;
  
  // Calculate rotation angle
  const angle = calculateWristAngle(landmarks);
  
  // Create gradient for feathering
  const gradient = ctx.createRadialGradient(
    center.x, center.y, Math.max(radiusX, radiusY) - featherRadius,
    center.x, center.y, Math.max(radiusX, radiusY)
  );
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
  
  // Draw ellipse
  ctx.save();
  ctx.translate(center.x, center.y);
  ctx.rotate(angle);
  ctx.scale(radiusX, radiusY);
  
  ctx.beginPath();
  ctx.arc(0, 0, 1, 0, 2 * Math.PI);
  ctx.fillStyle = gradient;
  ctx.fill();
  
  ctx.restore();
};

/**
 * Create a polygon mask around the wrist
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Array} landmarks - Pixel coordinates of wrist landmarks
 * @param {number} expandFactor - Factor to expand the polygon
 * @param {number} featherRadius - Feathering radius for soft edges
 */
const createPolygonMask = (ctx, landmarks, expandFactor, featherRadius) => {
  // Calculate wrist center
  const center = calculateWristCenter(landmarks);
  
  // Create polygon points around wrist landmarks
  const polygonPoints = landmarks.map(landmark => {
    // Expand points away from center
    const dx = landmark.x - center.x;
    const dy = landmark.y - center.y;
    return {
      x: center.x + dx * expandFactor,
      y: center.y + dy * expandFactor
    };
  });
  
  // Sort points by angle to create proper polygon
  polygonPoints.sort((a, b) => {
    const angleA = Math.atan2(a.y - center.y, a.x - center.x);
    const angleB = Math.atan2(b.y - center.y, b.x - center.x);
    return angleA - angleB;
  });
  
  // Draw polygon with feathering
  ctx.save();
  
  // Create clipping path
  ctx.beginPath();
  ctx.moveTo(polygonPoints[0].x, polygonPoints[0].y);
  for (let i = 1; i < polygonPoints.length; i++) {
    ctx.lineTo(polygonPoints[i].x, polygonPoints[i].y);
  }
  ctx.closePath();
  ctx.clip();
  
  // Fill with gradient for feathering
  const maxDistance = Math.max(
    ...polygonPoints.map(p => Math.sqrt(
      Math.pow(p.x - center.x, 2) + Math.pow(p.y - center.y, 2)
    ))
  );
  
  const gradient = ctx.createRadialGradient(
    center.x, center.y, maxDistance - featherRadius,
    center.x, center.y, maxDistance
  );
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);
  
  ctx.restore();
};

/**
 * Create a gradient mask around the wrist
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Array} landmarks - Pixel coordinates of wrist landmarks
 * @param {number} expandFactor - Factor to expand the gradient
 * @param {number} featherRadius - Feathering radius for soft edges
 */
const createGradientMask = (ctx, landmarks, expandFactor, featherRadius) => {
  // Calculate wrist center
  const center = calculateWristCenter(landmarks);
  
  // Calculate maximum distance from center to landmarks
  const maxDistance = Math.max(
    ...landmarks.map(landmark => Math.sqrt(
      Math.pow(landmark.x - center.x, 2) + Math.pow(landmark.y - center.y, 2)
    ))
  ) * expandFactor;
  
  // Create radial gradient
  const gradient = ctx.createRadialGradient(
    center.x, center.y, 0,
    center.x, center.y, maxDistance + featherRadius
  );
  
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
  gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.8)');
  gradient.addColorStop(0.9, 'rgba(255, 255, 255, 0.3)');
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
  
  // Draw gradient
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);
};

/**
 * Add finger occlusion to the mask
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Array} landmarks - All hand landmarks
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 */
const addFingerOcclusion = (ctx, landmarks, width, height) => {
  // Get finger tip landmarks
  const fingerTips = [
    landmarks[HAND_LANDMARKS.THUMB_TIP],
    landmarks[HAND_LANDMARKS.INDEX_FINGER_TIP],
    landmarks[HAND_LANDMARKS.MIDDLE_FINGER_TIP],
    landmarks[HAND_LANDMARKS.RING_FINGER_TIP],
    landmarks[HAND_LANDMARKS.PINKY_TIP]
  ];
  
  // Convert to pixel coordinates
  const pixelFingerTips = fingerTips.map(tip => ({
    x: tip.x * width,
    y: tip.y * height,
    z: tip.z
  }));
  
  // Add small circular masks for finger tips
  pixelFingerTips.forEach(tip => {
    const gradient = ctx.createRadialGradient(
      tip.x, tip.y, 0,
      tip.x, tip.y, 15
    );
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.5)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(tip.x, tip.y, 15, 0, 2 * Math.PI);
    ctx.fill();
  });
};

/**
 * Calculate the center of the wrist from landmarks
 * @param {Array} landmarks - Wrist landmarks in pixel coordinates
 * @returns {Object} - Center point {x, y}
 */
const calculateWristCenter = (landmarks) => {
  const sumX = landmarks.reduce((sum, landmark) => sum + landmark.x, 0);
  const sumY = landmarks.reduce((sum, landmark) => sum + landmark.y, 0);
  
  return {
    x: sumX / landmarks.length,
    y: sumY / landmarks.length
  };
};

/**
 * Calculate wrist dimensions from landmarks
 * @param {Array} landmarks - Wrist landmarks in pixel coordinates
 * @returns {Object} - Dimensions {width, height}
 */
const calculateWristDimensions = (landmarks) => {
  const xCoords = landmarks.map(landmark => landmark.x);
  const yCoords = landmarks.map(landmark => landmark.y);
  
  const width = Math.max(...xCoords) - Math.min(...xCoords);
  const height = Math.max(...yCoords) - Math.min(...yCoords);
  
  return { width, height };
};

/**
 * Calculate the rotation angle of the wrist
 * @param {Array} landmarks - Wrist landmarks in pixel coordinates
 * @returns {number} - Angle in radians
 */
const calculateWristAngle = (landmarks) => {
  // Use thumb CMC and pinky MCP to determine wrist orientation
  const thumb = landmarks.find(l => l === landmarks[1]); // Assuming thumb CMC is second
  const pinky = landmarks.find(l => l === landmarks[5]); // Assuming pinky MCP is last
  
  if (!thumb || !pinky) {
    return 0;
  }
  
  return Math.atan2(pinky.y - thumb.y, pinky.x - thumb.x);
};

/**
 * Apply wrist mask to an image for occlusion effects
 * @param {HTMLCanvasElement} sourceCanvas - Source image canvas
 * @param {ImageData} mask - Wrist mask
 * @param {Object} options - Application options
 * @returns {HTMLCanvasElement} - Canvas with mask applied
 */
export const applyWristMask = (sourceCanvas, mask, options = {}) => {
  const {
    blendMode = 'multiply',
    opacity = 1.0,
    invert = false
  } = options;
  
  // Create result canvas
  const resultCanvas = document.createElement('canvas');
  resultCanvas.width = sourceCanvas.width;
  resultCanvas.height = sourceCanvas.height;
  const resultCtx = resultCanvas.getContext('2d');
  
  // Draw source image
  resultCtx.drawImage(sourceCanvas, 0, 0);
  
  // Get source image data
  const sourceImageData = resultCtx.getImageData(0, 0, resultCanvas.width, resultCanvas.height);
  const sourceData = sourceImageData.data;
  const maskData = mask.data;
  
  // Apply mask
  for (let i = 0; i < sourceData.length; i += 4) {
    const maskValue = invert ? (255 - maskData[i]) : maskData[i];
    const alpha = (maskValue / 255) * opacity;
    
    switch (blendMode) {
      case 'multiply':
        sourceData[i] *= alpha;     // Red
        sourceData[i + 1] *= alpha; // Green
        sourceData[i + 2] *= alpha; // Blue
        break;
      case 'overlay':
        sourceData[i + 3] *= alpha; // Alpha
        break;
      case 'screen':
        sourceData[i] = 255 - ((255 - sourceData[i]) * alpha);
        sourceData[i + 1] = 255 - ((255 - sourceData[i + 1]) * alpha);
        sourceData[i + 2] = 255 - ((255 - sourceData[i + 2]) * alpha);
        break;
    }
  }
  
  // Put modified image data back
  resultCtx.putImageData(sourceImageData, 0, 0);
  
  return resultCanvas;
};

/**
 * Create depth-based occlusion mask
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @param {number} watchZ - Z-coordinate of the watch
 * @returns {ImageData} - Depth-based occlusion mask
 */
export const createDepthOcclusionMask = (landmarks, width, height, watchZ) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  
  // Clear canvas
  ctx.clearRect(0, 0, width, height);
  
  if (!landmarks || landmarks.length === 0) {
    return ctx.getImageData(0, 0, width, height);
  }
  
  // Create depth map
  const imageData = ctx.createImageData(width, height);
  const data = imageData.data;
  
  // For each pixel, determine if it should be occluded based on depth
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const normalizedX = x / width;
      const normalizedY = y / height;
      
      // Find closest landmark
      let closestZ = Infinity;
      landmarks.forEach(landmark => {
        const distance = Math.sqrt(
          Math.pow(landmark.x - normalizedX, 2) +
          Math.pow(landmark.y - normalizedY, 2)
        );
        
        if (distance < 0.1) { // Within influence radius
          const interpolatedZ = landmark.z * (1 - distance / 0.1);
          closestZ = Math.min(closestZ, interpolatedZ);
        }
      });
      
      // Determine occlusion
      const pixelIndex = (y * width + x) * 4;
      if (closestZ < watchZ) {
        // Hand is in front of watch - create occlusion
        data[pixelIndex] = 255;     // Red
        data[pixelIndex + 1] = 255; // Green
        data[pixelIndex + 2] = 255; // Blue
        data[pixelIndex + 3] = 255; // Alpha
      } else {
        // Hand is behind watch - no occlusion
        data[pixelIndex] = 0;       // Red
        data[pixelIndex + 1] = 0;   // Green
        data[pixelIndex + 2] = 0;   // Blue
        data[pixelIndex + 3] = 0;   // Alpha
      }
    }
  }
  
  return imageData;
};

export default {
  createWristMask,
  applyWristMask,
  createDepthOcclusionMask
};
