/**
 * Posture-Aware Transforms Module
 * 
 * This module provides posture-aware transformations that adjust watch rotation
 * and positioning based on hand pose and wrist angle for maximum realism.
 */

import { HAND_LANDMARKS } from './handDetection';

/**
 * Calculate posture-aware transformations for watch placement
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} handPose - 2.5D hand pose data
 * @param {Object} options - Transform options
 * @returns {Object} - Transformation parameters
 */
export const calculatePostureTransforms = (landmarks, handPose, options = {}) => {
  if (!landmarks || landmarks.length === 0 || !handPose) {
    return {
      rotation: 0,
      translateX: 0,
      translateY: 0,
      scale: 1,
      perspective: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]
    };
  }

  const {
    adaptToWristCurvature = true,
    adaptToHandRotation = true,
    adaptToWristFlexion = true,
    adaptToFingerPosition = true
  } = options;

  // Calculate wrist curvature transforms
  const curvatureTransforms = adaptToWristCurvature
    ? calculateWristCurvatureTransforms(landmarks, handPose)
    : { rotation: 0, scale: 1 };

  // Calculate hand rotation transforms
  const rotationTransforms = adaptToHandRotation
    ? calculateHandRotationTransforms(landmarks, handPose)
    : { rotationX: 0, rotationY: 0, rotationZ: 0 };

  // Calculate wrist flexion transforms
  const flexionTransforms = adaptToWristFlexion
    ? calculateWristFlexionTransforms(landmarks, handPose)
    : { translateY: 0, rotationX: 0 };

  // Calculate finger position transforms
  const fingerTransforms = adaptToFingerPosition
    ? calculateFingerPositionTransforms(landmarks, handPose)
    : { translateX: 0, translateY: 0 };

  // Combine all transforms
  const combinedTransforms = combineTransforms(
    curvatureTransforms,
    rotationTransforms,
    flexionTransforms,
    fingerTransforms
  );

  return combinedTransforms;
};

/**
 * Calculate transforms based on wrist curvature
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} handPose - 2.5D hand pose data
 * @returns {Object} - Curvature transformation parameters
 */
const calculateWristCurvatureTransforms = (landmarks, handPose) => {
  // Get key wrist landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const thumbCMC = landmarks[HAND_LANDMARKS.THUMB_CMC];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];

  // Calculate wrist curvature (approximated by the angle between wrist landmarks)
  const wristVector1 = {
    x: thumbCMC.x - wrist.x,
    y: thumbCMC.y - wrist.y,
    z: thumbCMC.z - wrist.z
  };

  const wristVector2 = {
    x: pinkyMCP.x - wrist.x,
    y: pinkyMCP.y - wrist.y,
    z: pinkyMCP.z - wrist.z
  };

  // Calculate the angle between the two vectors
  const dotProduct = wristVector1.x * wristVector2.x + wristVector1.y * wristVector2.y + wristVector1.z * wristVector2.z;
  const magnitude1 = Math.sqrt(wristVector1.x * wristVector1.x + wristVector1.y * wristVector1.y + wristVector1.z * wristVector1.z);
  const magnitude2 = Math.sqrt(wristVector2.x * wristVector2.x + wristVector2.y * wristVector2.y + wristVector2.z * wristVector2.z);
  
  const cosAngle = dotProduct / (magnitude1 * magnitude2);
  const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  
  // Calculate curvature factor (0 = flat, 1 = highly curved)
  const curvatureFactor = 1 - (angle / Math.PI);
  
  // Calculate rotation based on curvature
  // For a curved wrist, the watch should rotate slightly to follow the curvature
  const rotation = curvatureFactor * 5; // Up to 5 degrees of rotation
  
  // Calculate scale based on curvature
  // For a curved wrist, the watch might appear slightly smaller
  const scale = 1 - (curvatureFactor * 0.05); // Up to 5% smaller
  
  return {
    rotation,
    scale,
    curvatureFactor
  };
};

/**
 * Calculate transforms based on hand rotation
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} handPose - 2.5D hand pose data
 * @returns {Object} - Rotation transformation parameters
 */
const calculateHandRotationTransforms = (landmarks, handPose) => {
  // Use hand orientation from handPose
  const { pitch, yaw, roll } = handPose.handOrientation;
  
  // Convert to radians
  const rotationX = pitch * (Math.PI / 180);
  const rotationY = yaw * (Math.PI / 180);
  const rotationZ = roll * (Math.PI / 180);
  
  return {
    rotationX,
    rotationY,
    rotationZ
  };
};

/**
 * Calculate transforms based on wrist flexion
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} handPose - 2.5D hand pose data
 * @returns {Object} - Flexion transformation parameters
 */
const calculateWristFlexionTransforms = (landmarks, handPose) => {
  // Get key landmarks
  const wrist = landmarks[HAND_LANDMARKS.WRIST];
  const middleMCP = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
  
  // Calculate wrist flexion (angle between wrist and middle finger MCP)
  const wristToMiddle = {
    x: middleMCP.x - wrist.x,
    y: middleMCP.y - wrist.y,
    z: middleMCP.z - wrist.z
  };
  
  // Calculate angle with horizontal plane
  const horizontalAngle = Math.atan2(wristToMiddle.y, Math.sqrt(wristToMiddle.x * wristToMiddle.x + wristToMiddle.z * wristToMiddle.z));
  
  // Calculate translation based on flexion
  // For a flexed wrist, the watch should move slightly up or down
  const translateY = horizontalAngle * 20; // Up to 20px translation
  
  // Calculate rotation based on flexion
  // For a flexed wrist, the watch should rotate to follow the wrist angle
  const rotationX = horizontalAngle * (180 / Math.PI) * 0.5; // Up to 50% of the actual angle
  
  return {
    translateY,
    rotationX,
    flexionAngle: horizontalAngle * (180 / Math.PI)
  };
};

/**
 * Calculate transforms based on finger position
 * @param {Array} landmarks - Hand landmarks from MediaPipe
 * @param {Object} handPose - 2.5D hand pose data
 * @returns {Object} - Finger position transformation parameters
 */
const calculateFingerPositionTransforms = (landmarks, handPose) => {
  // Get key landmarks
  const indexMCP = landmarks[HAND_LANDMARKS.INDEX_FINGER_MCP];
  const pinkyMCP = landmarks[HAND_LANDMARKS.PINKY_MCP];
  const middleMCP = landmarks[HAND_LANDMARKS.MIDDLE_FINGER_MCP];
  
  // Calculate center of palm
  const palmCenter = {
    x: (indexMCP.x + pinkyMCP.x) / 2,
    y: (indexMCP.y + pinkyMCP.y) / 2,
    z: (indexMCP.z + pinkyMCP.z) / 2
  };
  
  // Calculate translation based on finger position
  // The watch should be centered on the wrist, slightly offset towards the palm
  const translateX = (middleMCP.x - palmCenter.x) * 10; // Small horizontal adjustment
  const translateY = (middleMCP.y - palmCenter.y) * 20; // Vertical adjustment towards palm
  
  return {
    translateX,
    translateY
  };
};

/**
 * Combine all transformation parameters
 * @param {Object} curvatureTransforms - Wrist curvature transforms
 * @param {Object} rotationTransforms - Hand rotation transforms
 * @param {Object} flexionTransforms - Wrist flexion transforms
 * @param {Object} fingerTransforms - Finger position transforms
 * @returns {Object} - Combined transformation parameters
 */
const combineTransforms = (
  curvatureTransforms,
  rotationTransforms,
  flexionTransforms,
  fingerTransforms
) => {
  // Combine rotation values
  const rotationX = (rotationTransforms.rotationX || 0) + (flexionTransforms.rotationX || 0);
  const rotationY = rotationTransforms.rotationY || 0;
  const rotationZ = (rotationTransforms.rotationZ || 0) + (curvatureTransforms.rotation || 0);
  
  // Combine translation values
  const translateX = (fingerTransforms.translateX || 0);
  const translateY = (fingerTransforms.translateY || 0) + (flexionTransforms.translateY || 0);
  
  // Combine scale values
  const scale = curvatureTransforms.scale || 1;
  
  // Calculate perspective transform matrix
  const perspective = calculatePerspectiveMatrix(rotationX, rotationY, rotationZ);
  
  return {
    rotation: rotationZ,
    rotationX,
    rotationY,
    rotationZ,
    translateX,
    translateY,
    scale,
    perspective
  };
};

/**
 * Calculate perspective transformation matrix
 * @param {number} rotationX - Rotation around X-axis in radians
 * @param {number} rotationY - Rotation around Y-axis in radians
 * @param {number} rotationZ - Rotation around Z-axis in radians
 * @returns {Array} - 4x4 perspective matrix
 */
const calculatePerspectiveMatrix = (rotationX, rotationY, rotationZ) => {
  // Convert to radians
  const rx = rotationX;
  const ry = rotationY;
  const rz = rotationZ;
  
  // Calculate sine and cosine values
  const sx = Math.sin(rx);
  const cx = Math.cos(rx);
  const sy = Math.sin(ry);
  const cy = Math.cos(ry);
  const sz = Math.sin(rz);
  const cz = Math.cos(rz);
  
  // Calculate rotation matrices
  // Rotation around X-axis
  const rotX = [
    1, 0, 0, 0,
    0, cx, sx, 0,
    0, -sx, cx, 0,
    0, 0, 0, 1
  ];
  
  // Rotation around Y-axis
  const rotY = [
    cy, 0, -sy, 0,
    0, 1, 0, 0,
    sy, 0, cy, 0,
    0, 0, 0, 1
  ];
  
  // Rotation around Z-axis
  const rotZ = [
    cz, sz, 0, 0,
    -sz, cz, 0, 0,
    0, 0, 1, 0,
    0, 0, 0, 1
  ];
  
  // Combine rotation matrices (X * Y * Z)
  return multiplyMatrices(rotX, multiplyMatrices(rotY, rotZ));
};

/**
 * Multiply two 4x4 matrices
 * @param {Array} a - First matrix (16 elements)
 * @param {Array} b - Second matrix (16 elements)
 * @returns {Array} - Result matrix (16 elements)
 */
const multiplyMatrices = (a, b) => {
  const result = new Array(16).fill(0);
  
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      for (let i = 0; i < 4; i++) {
        result[row * 4 + col] += a[row * 4 + i] * b[i * 4 + col];
      }
    }
  }
  
  return result;
};

/**
 * Apply posture transforms to CSS style object
 * @param {Object} transforms - Transformation parameters
 * @param {Object} options - Application options
 * @returns {Object} - CSS style object
 */
export const applyPostureTransformsToStyle = (transforms, options = {}) => {
  const {
    useGPU = true,
    unit = 'px',
    prefix = true
  } = options;
  
  if (!transforms) {
    return {};
  }
  
  const {
    rotation,
    rotationX,
    rotationY,
    rotationZ,
    translateX,
    translateY,
    scale,
    perspective
  } = transforms;
  
  // Create transform string
  let transform = '';
  
  // Add perspective
  if (useGPU) {
    transform += `perspective(1000${unit}) `;
  }
  
  // Add translations
  if (translateX || translateY) {
    transform += `translate(${translateX || 0}${unit}, ${translateY || 0}${unit}) `;
  }
  
  // Add rotations
  if (rotationX) {
    transform += `rotateX(${rotationX}deg) `;
  }
  
  if (rotationY) {
    transform += `rotateY(${rotationY}deg) `;
  }
  
  if (rotation || rotationZ) {
    const rotZ = rotation || rotationZ;
    transform += `rotateZ(${rotZ}deg) `;
  }
  
  // Add scale
  if (scale !== 1) {
    transform += `scale(${scale}) `;
  }
  
  // Create style object
  const style = {
    transform: transform.trim()
  };
  
  // Add prefixed versions if needed
  if (prefix) {
    style.WebkitTransform = style.transform;
    style.MozTransform = style.transform;
    style.msTransform = style.transform;
  }
  
  // Add matrix3d transform for more precise control
  if (perspective && useGPU) {
    const matrix3d = `matrix3d(${perspective.join(',')})`;
    style.transform = matrix3d;
    
    if (prefix) {
      style.WebkitTransform = matrix3d;
      style.MozTransform = matrix3d;
      style.msTransform = matrix3d;
    }
  }
  
  return style;
};

/**
 * Create CSS filter effects based on hand pose
 * @param {Object} handPose - 2.5D hand pose data
 * @param {Object} options - Filter options
 * @returns {Object} - CSS style object with filters
 */
export const createPostureAwareFilters = (handPose, options = {}) => {
  const {
    enableShadow = true,
    enableBlur = true,
    enableBrightness = true,
    enableContrast = true
  } = options;
  
  if (!handPose) {
    return {};
  }
  
  const { handOrientation, depthInfo } = handPose;
  
  let filters = [];
  
  // Add drop shadow based on hand orientation
  if (enableShadow) {
    const shadowX = Math.sin(handOrientation.yaw * (Math.PI / 180)) * 5;
    const shadowY = Math.sin(handOrientation.pitch * (Math.PI / 180)) * 5;
    filters.push(`drop-shadow(${shadowX}px ${shadowY}px 5px rgba(0,0,0,0.3))`);
  }
  
  // Add blur based on depth
  if (enableBlur && depthInfo) {
    const blurAmount = Math.abs(depthInfo.avgWristZ - 0.5) * 2;
    if (blurAmount > 0.2) {
      filters.push(`blur(${blurAmount}px)`);
    }
  }
  
  // Add brightness based on lighting conditions
  if (enableBrightness) {
    const brightness = 1.0 + (handOrientation.pitch / 90) * 0.2;
    filters.push(`brightness(${brightness})`);
  }
  
  // Add contrast based on depth
  if (enableContrast && depthInfo) {
    const contrast = 1.0 + (depthInfo.avgWristZ * 0.2);
    filters.push(`contrast(${contrast})`);
  }
  
  return {
    filter: filters.join(' '),
    WebkitFilter: filters.join(' ')
  };
};

export default {
  calculatePostureTransforms,
  applyPostureTransformsToStyle,
  createPostureAwareFilters
};
