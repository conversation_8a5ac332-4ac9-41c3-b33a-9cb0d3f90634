/**
 * Tangiblee-Style Interface Component
 * 
 * This component provides a professional measurement interface similar to Tangiblee,
 * with real-time hand tracking feedback and measurement overlays.
 */

import React, { useState, useEffect, useRef } from 'react';

const TangibleeInterface = ({
  handData,
  realWorldMeasurements,
  watchSpecs,
  isVisible = true,
  onMeasurementUpdate
}) => {
  const [showMeasurements, setShowMeasurements] = useState(true);
  const [measurementMode, setMeasurementMode] = useState('wrist'); // 'wrist', 'watch', 'comparison'
  const overlayRef = useRef(null);

  // Update measurements when hand data changes
  useEffect(() => {
    if (handData && realWorldMeasurements && onMeasurementUpdate) {
      onMeasurementUpdate({
        wristWidth: realWorldMeasurements.wristWidth,
        watchDiameter: watchSpecs?.caseDiameter || 42,
        confidence: handData.confidence
      });
    }
  }, [handData, realWorldMeasurements, watchSpecs, onMeasurementUpdate]);

  if (!isVisible) return null;

  return (
    <div className="tangiblee-interface" style={styles.container}>
      {/* Measurement Display Panel */}
      <div style={styles.measurementPanel}>
        <div style={styles.panelHeader}>
          <h3 style={styles.panelTitle}>Measurements</h3>
          <div style={styles.modeSelector}>
            <button
              style={{
                ...styles.modeButton,
                ...(measurementMode === 'wrist' ? styles.modeButtonActive : {})
              }}
              onClick={() => setMeasurementMode('wrist')}
            >
              Wrist
            </button>
            <button
              style={{
                ...styles.modeButton,
                ...(measurementMode === 'watch' ? styles.modeButtonActive : {})
              }}
              onClick={() => setMeasurementMode('watch')}
            >
              Watch
            </button>
            <button
              style={{
                ...styles.modeButton,
                ...(measurementMode === 'comparison' ? styles.modeButtonActive : {})
              }}
              onClick={() => setMeasurementMode('comparison')}
            >
              Compare
            </button>
          </div>
        </div>

        <div style={styles.panelContent}>
          {measurementMode === 'wrist' && (
            <WristMeasurements
              realWorldMeasurements={realWorldMeasurements}
              handData={handData}
            />
          )}
          {measurementMode === 'watch' && (
            <WatchMeasurements
              watchSpecs={watchSpecs}
              realWorldMeasurements={realWorldMeasurements}
            />
          )}
          {measurementMode === 'comparison' && (
            <ComparisonMeasurements
              watchSpecs={watchSpecs}
              realWorldMeasurements={realWorldMeasurements}
            />
          )}
        </div>
      </div>

      {/* Hand Tracking Status */}
      <HandTrackingStatus handData={handData} />

      {/* Measurement Overlays */}
      {showMeasurements && (
        <MeasurementOverlays
          ref={overlayRef}
          handData={handData}
          realWorldMeasurements={realWorldMeasurements}
          watchSpecs={watchSpecs}
          measurementMode={measurementMode}
        />
      )}

      {/* Toggle Button */}
      <button
        style={styles.toggleButton}
        onClick={() => setShowMeasurements(!showMeasurements)}
        title={showMeasurements ? 'Hide Measurements' : 'Show Measurements'}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
          <path d="M3 17h18v-2H3v2zm0-6h18v-2H3v2zm0-4v2h18V7H3z"/>
        </svg>
      </button>
    </div>
  );
};

// Wrist Measurements Component
const WristMeasurements = ({ realWorldMeasurements, handData }) => {
  if (!realWorldMeasurements) {
    return (
      <div style={styles.noData}>
        <p>Position your hand to see measurements</p>
      </div>
    );
  }

  return (
    <div style={styles.measurementGrid}>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Width</span>
        <span style={styles.measurementValue}>
          {Math.round(realWorldMeasurements.wristWidth)}mm
        </span>
      </div>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Circumference</span>
        <span style={styles.measurementValue}>
          {Math.round(realWorldMeasurements.wristCircumference)}mm
        </span>
      </div>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Confidence</span>
        <span style={styles.measurementValue}>
          {Math.round((handData?.confidence || 0) * 100)}%
        </span>
      </div>
    </div>
  );
};

// Watch Measurements Component
const WatchMeasurements = ({ watchSpecs, realWorldMeasurements }) => {
  if (!watchSpecs) {
    return (
      <div style={styles.noData}>
        <p>Select a watch to see specifications</p>
      </div>
    );
  }

  return (
    <div style={styles.measurementGrid}>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Case Diameter</span>
        <span style={styles.measurementValue}>
          {watchSpecs.caseDiameter || watchSpecs.dialSize || 42}mm
        </span>
      </div>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Thickness</span>
        <span style={styles.measurementValue}>
          {watchSpecs.thickness || 12}mm
        </span>
      </div>
      <div style={styles.measurementItem}>
        <span style={styles.measurementLabel}>Lug Width</span>
        <span style={styles.measurementValue}>
          {watchSpecs.lugWidth || 20}mm
        </span>
      </div>
    </div>
  );
};

// Comparison Measurements Component
const ComparisonMeasurements = ({ watchSpecs, realWorldMeasurements }) => {
  if (!watchSpecs || !realWorldMeasurements) {
    return (
      <div style={styles.noData}>
        <p>Need both wrist and watch data for comparison</p>
      </div>
    );
  }

  const watchDiameter = watchSpecs.caseDiameter || watchSpecs.dialSize || 42;
  const wristWidth = realWorldMeasurements.wristWidth;
  const fitRatio = watchDiameter / wristWidth;
  
  let fitDescription = 'Perfect Fit';
  let fitColor = '#4CAF50';
  
  if (fitRatio < 0.6) {
    fitDescription = 'Too Small';
    fitColor = '#FF9800';
  } else if (fitRatio > 0.9) {
    fitDescription = 'Too Large';
    fitColor = '#F44336';
  } else if (fitRatio < 0.7) {
    fitDescription = 'Small Fit';
    fitColor = '#FFC107';
  } else if (fitRatio > 0.8) {
    fitDescription = 'Large Fit';
    fitColor = '#FF9800';
  }

  return (
    <div style={styles.comparisonContainer}>
      <div style={styles.fitIndicator}>
        <div style={{...styles.fitBar, backgroundColor: fitColor}}>
          <div style={{...styles.fitMarker, left: `${fitRatio * 100}%`}} />
        </div>
        <span style={{...styles.fitLabel, color: fitColor}}>
          {fitDescription}
        </span>
      </div>
      
      <div style={styles.comparisonGrid}>
        <div style={styles.comparisonItem}>
          <span style={styles.comparisonLabel}>Watch/Wrist Ratio</span>
          <span style={styles.comparisonValue}>
            {(fitRatio * 100).toFixed(1)}%
          </span>
        </div>
        <div style={styles.comparisonItem}>
          <span style={styles.comparisonLabel}>Size Difference</span>
          <span style={styles.comparisonValue}>
            {Math.abs(watchDiameter - wristWidth).toFixed(1)}mm
          </span>
        </div>
      </div>
    </div>
  );
};

// Hand Tracking Status Component
const HandTrackingStatus = ({ handData }) => {
  if (!handData) return null;

  const statusColor = handData.detected
    ? handData.confidence > 0.8 ? '#4CAF50' : '#FFC107'
    : '#F44336';

  const statusText = handData.detected
    ? handData.confidence > 0.8 ? 'Excellent Tracking' : 'Good Tracking'
    : 'No Hand Detected';

  return (
    <div style={styles.trackingStatus}>
      <div style={{...styles.statusIndicator, backgroundColor: statusColor}} />
      <span style={styles.statusText}>{statusText}</span>
    </div>
  );
};

// Measurement Overlays Component
const MeasurementOverlays = React.forwardRef(({
  handData,
  realWorldMeasurements,
  watchSpecs,
  measurementMode
}, ref) => {
  if (!handData?.detected || !realWorldMeasurements) return null;

  return (
    <div ref={ref} style={styles.overlayContainer}>
      {/* Wrist Width Lines */}
      {(measurementMode === 'wrist' || measurementMode === 'comparison') && (
        <WristMeasurementLines
          handData={handData}
          realWorldMeasurements={realWorldMeasurements}
        />
      )}

      {/* Watch Measurement Lines */}
      {(measurementMode === 'watch' || measurementMode === 'comparison') && watchSpecs && (
        <WatchMeasurementLines
          handData={handData}
          watchSpecs={watchSpecs}
        />
      )}
    </div>
  );
});

// Wrist Measurement Lines Component
const WristMeasurementLines = ({ handData, realWorldMeasurements }) => {
  const wristPosition = handData.wristPosition;
  if (!wristPosition) return null;

  return (
    <>
      {/* Vertical measurement lines */}
      <div style={{
        ...styles.measurementLine,
        left: `${(wristPosition.x - 0.1) * 100}%`,
        top: `${(wristPosition.y - 0.05) * 100}%`,
        height: '10%',
        borderLeft: '2px solid #2196F3'
      }} />
      <div style={{
        ...styles.measurementLine,
        left: `${(wristPosition.x + 0.1) * 100}%`,
        top: `${(wristPosition.y - 0.05) * 100}%`,
        height: '10%',
        borderLeft: '2px solid #2196F3'
      }} />

      {/* Measurement label */}
      <div style={{
        ...styles.measurementLabel,
        left: `${wristPosition.x * 100}%`,
        top: `${(wristPosition.y + 0.08) * 100}%`,
        transform: 'translateX(-50%)'
      }}>
        {Math.round(realWorldMeasurements.wristWidth)}mm
      </div>
    </>
  );
};

// Watch Measurement Lines Component
const WatchMeasurementLines = ({ handData, watchSpecs }) => {
  const wristPosition = handData.wristPosition;
  if (!wristPosition) return null;

  const watchDiameter = watchSpecs.caseDiameter || watchSpecs.dialSize || 42;

  return (
    <>
      {/* Horizontal measurement lines */}
      <div style={{
        ...styles.measurementLine,
        left: `${(wristPosition.x - 0.08) * 100}%`,
        top: `${(wristPosition.y - 0.1) * 100}%`,
        width: '16%',
        borderTop: '2px solid #FF9800'
      }} />
      <div style={{
        ...styles.measurementLine,
        left: `${(wristPosition.x - 0.08) * 100}%`,
        top: `${(wristPosition.y + 0.1) * 100}%`,
        width: '16%',
        borderTop: '2px solid #FF9800'
      }} />

      {/* Watch diameter label */}
      <div style={{
        ...styles.measurementLabel,
        left: `${(wristPosition.x + 0.12) * 100}%`,
        top: `${wristPosition.y * 100}%`,
        transform: 'translateY(-50%)',
        backgroundColor: '#FF9800'
      }}>
        {watchDiameter}mm
      </div>
    </>
  );
};

// Styles
const styles = {
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
    zIndex: 10
  },
  measurementPanel: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    width: '280px',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: '12px',
    padding: '16px',
    pointerEvents: 'auto',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  panelHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '16px'
  },
  panelTitle: {
    color: 'white',
    fontSize: '16px',
    fontWeight: '600',
    margin: 0
  },
  modeSelector: {
    display: 'flex',
    gap: '4px'
  },
  modeButton: {
    padding: '4px 8px',
    fontSize: '12px',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    transition: 'all 0.2s'
  },
  modeButtonActive: {
    backgroundColor: '#2196F3',
    color: 'white'
  },
  panelContent: {
    minHeight: '80px'
  },
  measurementGrid: {
    display: 'grid',
    gap: '12px'
  },
  measurementItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  measurementLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: '14px'
  },
  measurementValue: {
    color: 'white',
    fontSize: '16px',
    fontWeight: '600'
  },
  noData: {
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: '14px',
    padding: '20px 0'
  },
  comparisonContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px'
  },
  fitIndicator: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  fitBar: {
    height: '8px',
    borderRadius: '4px',
    position: 'relative'
  },
  fitMarker: {
    position: 'absolute',
    top: '-2px',
    width: '4px',
    height: '12px',
    backgroundColor: 'white',
    borderRadius: '2px',
    transform: 'translateX(-50%)'
  },
  fitLabel: {
    fontSize: '14px',
    fontWeight: '600',
    textAlign: 'center'
  },
  comparisonGrid: {
    display: 'grid',
    gap: '8px'
  },
  comparisonItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  comparisonLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: '13px'
  },
  comparisonValue: {
    color: 'white',
    fontSize: '14px',
    fontWeight: '600'
  },
  trackingStatus: {
    position: 'absolute',
    bottom: '20px',
    left: '20px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: '8px 12px',
    borderRadius: '20px',
    pointerEvents: 'auto'
  },
  statusIndicator: {
    width: '8px',
    height: '8px',
    borderRadius: '50%'
  },
  statusText: {
    color: 'white',
    fontSize: '12px',
    fontWeight: '500'
  },
  toggleButton: {
    position: 'absolute',
    bottom: '20px',
    right: '20px',
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'auto',
    transition: 'all 0.2s'
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none'
  },
  measurementLine: {
    position: 'absolute',
    pointerEvents: 'none'
  },
  measurementLabel: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    padding: '4px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: '600',
    pointerEvents: 'none',
    whiteSpace: 'nowrap'
  }
};

export default TangibleeInterface;
