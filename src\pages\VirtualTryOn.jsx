import React, { useState, useEffect, useRef } from 'react';
import { QRCodeSVG } from 'qrcode.react'; // Fix the import
import { useParams, useSearchParams } from 'react-router-dom';
import { loadAllWatches } from '../utils/imageLoader';
import * as realisticTryOn from '../utils/realisticTryOn';
import TangibleeInterface from '../components/TangibleeInterface';

// Add CSS for range slider styling and animations
const sliderCSS = `
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-webkit-slider-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
  }

  input[type="range"]::-moz-range-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
    border: none;
  }

  input[type="range"]:focus {
    outline: none;
  }

  input[type="range"]:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }

  input[type="range"]:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }

  /* Animations */
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  /* Product card hover effects */
  .product-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .product-card-hover:hover .product-image {
    transform: scale(1.1);
  }

  /* Smooth scrolling for product grid */
  .tangiblee-product-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for horizontal product grid */
  .product-grid-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Wrist display hover effect */
  .wrist-display-hover:hover {
    transform: translateX(-50%) translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.5);
    background-color: rgba(0, 0, 0, 0.95);
  }

  /* Switch styles */
  .switch-container {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }

  .switch-container input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 34px;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .switch-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  input:checked + .switch-slider {
    background-color: #2D8C88;
  }

  input:checked + .switch-slider:before {
    transform: translateX(26px);
  }

  input:disabled + .switch-slider {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .switch-label {
    font-size: 12px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }
`;

// Inject CSS (only once)
if (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'wrist-size-slider-styles';
  styleElement.textContent = sliderCSS;
  document.head.appendChild(styleElement);
}

const VirtualTryon = ({ onBackToHome }) => {
  // Get URL parameters
  const params = useParams();
  const [searchParams] = useSearchParams();
  
  // Refs for DOM elements
  const videoRef = useRef(null);
  const capturedImageRef = useRef(null);
  const canvasRef = useRef(null);

  // State variables
  const [isCaptured, setIsCaptured] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isRightHand, setIsRightHand] = useState(false);
  const [showProductSelection, setShowProductSelection] = useState(false);
  const [activeTab, setActiveTab] = useState('Watches');
  const [showHandGuide, setShowHandGuide] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [showDisclaimerPopup, setShowDisclaimerPopup] = useState(true);
  const [isUsingModelImage, setIsUsingModelImage] = useState(false);
  const [shareCopied, setShareCopied] = useState(false);

  // Stacking state variables
  const [selectedWatch, setSelectedWatch] = useState(null);
  const [selectedBracelet, setSelectedBracelet] = useState(null);
  const [isStackingSwapped, setIsStackingSwapped] = useState(false); // false = bracelet left, watch right; true = watch left, bracelet right

  // Watch size customization state
  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'
  const [userWristSize, setUserWristSize] = useState(50); // Default men's wrist size in mm
  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal

  // Autocapture state variables
  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isHandInPosition, setIsHandInPosition] = useState(false);
  const [isCountdownActive, setIsCountdownActive] = useState(false);

  // Add new state for panel position
  const [panelPosition, setPanelPosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const panelRef = useRef(null);

  // Add state for dynamically loaded watches
  const [watches, setWatches] = useState([]);
  const [watchMeasurements, setWatchMeasurements] = useState({}); // Cache for segmented measurements

  // Realistic try-on state variables
  const [realisticTryOnInitialized, setRealisticTryOnInitialized] = useState(false);
  const [handData, setHandData] = useState(null);
  const [handPose, setHandPose] = useState(null);
  const [realWorldMeasurements, setRealWorldMeasurements] = useState(null);
  const [postureTransforms, setPostureTransforms] = useState(null);
  const [showTangibleeInterface, setShowTangibleeInterface] = useState(true);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);

  // Load watches dynamically with segmentation
  useEffect(() => {
    const loadWatches = async () => {
      try {
        const allWatches = await loadAllWatches();
        setWatches(allWatches);

        // Pre-segment watches for better performance (optional - can be done on-demand)
        // This is commented out to avoid blocking the initial load
        // const measurements = {};
        // for (const watch of allWatches.slice(0, 3)) { // Only pre-segment first 3 watches
        //   try {
        //     const measurement = await segmentAndMeasureWatch(watch.path || watch.image);
        //     if (measurement) {
        //       measurements[watch.id] = measurement;
        //     }
        //   } catch (error) {
        //     console.warn(`Failed to segment watch ${watch.id}:`, error);
        //   }
        // }
        // setWatchMeasurements(measurements);
      } catch (error) {
        console.error('Error loading watches:', error);
        // Fallback to empty array
        setWatches([]);
      }
    };

    loadWatches();
  }, []);

  // Handle URL parameters and query parameters
  useEffect(() => {
    // Get category from URL params or query params
    const categoryFromParams = params.category;
    const categoryFromQuery = searchParams.get('category');
    const productIdFromQuery = searchParams.get('productId');
    
    // Set active tab based on category
    if (categoryFromParams || categoryFromQuery) {
      const category = categoryFromParams || categoryFromQuery;
      const categoryMap = {
        'watches': 'Watches',
        'bracelets': 'Bracelets',
        'rings': 'Rings',
        'earrings': 'Earrings',
        'stacking': 'Stacking'
      };

      if (categoryMap[category]) {
        setActiveTab(categoryMap[category]);
      }
    }

    // If productId is provided, we'll handle it after products are loaded
    if (productIdFromQuery) {
      // This will be handled in the getCurrentProducts effect
    }
  }, [params.category, searchParams]);

  // Handle productId parameter after activeTab is set
  useEffect(() => {
    const productIdFromQuery = searchParams.get('productId');
    
    if (productIdFromQuery && watches.length > 0) {
      const product = watches.find(p => p.id === productIdFromQuery || p.name.toLowerCase().includes(productIdFromQuery.toLowerCase()));
      
      if (product) {
        setSelectedProduct(product);
        setShowProductSelection(true);
      }
    }
  }, [activeTab, searchParams, watches]);

  // Detect mobile device and set viewport height for Chrome mobile
  useEffect(() => {
    const checkDevice = () => {
      const isMobileDevice = window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
      setIsDesktop(!isMobileDevice);
    };

    // Fix viewport height for Chrome mobile
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    checkDevice();
    setVH();

    window.addEventListener('resize', () => {
      checkDevice();
      setVH();
    });

    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        setVH();
      }, 100);
    });

    return () => {
      window.removeEventListener('resize', checkDevice);
      window.removeEventListener('orientationchange', setVH);
    };
  }, []);

  // Realistic sizing configuration
  // Average adult wrist circumference: 165mm (men), 155mm (women)
  // Average wrist width when viewed from top: ~55-65mm
  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)
  // This means the circle represents approximately 60mm in real life

  // Universal wrist size configuration
  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view
  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width
  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width
  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling

  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units
  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width
  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height

  // Size configuration - Increased for better visibility and more realistic appearance
  const WATCH_WIDTH = 30; // percentage of container width - INCREASED for bigger, more realistic watches
  const BRACELET_WIDTH = 15; // percentage of container width
  const RING_WIDTH = 20; // percentage of container width
  const EARRING_WIDTH = 18; // percentage of container width
  const WATCH_HEIGHT = 44; // percentage of container height - INCREASED for bigger, more realistic watches
  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility
  const RING_HEIGHT = 25; // percentage of container height
  const EARRING_HEIGHT = 30; // percentage of container height

  // Updated: WATCH_WIDTH increased from 25% to 32% (+28% increase)
  // Updated: WATCH_HEIGHT increased from 38% to 48% (+26% increase)
  // This makes watches significantly bigger and more realistic for virtual try-on
  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale

  // Advanced watch image segmentation and measurement function
  const segmentAndMeasureWatch = async (imagePath) => {
    try {
      // Create a canvas to load and process the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      // Load image and wait for it to be ready
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imagePath;
      });

      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw image to canvas
      ctx.drawImage(img, 0, 0);

      // Get image data for processing
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Segment the watch from background (white/transparent background removal)
      const watchPixels = [];
      let minX = canvas.width, maxX = 0, minY = canvas.height, maxY = 0;

      for (let y = 0; y < canvas.height; y++) {
        for (let x = 0; x < canvas.width; x++) {
          const idx = (y * canvas.width + x) * 4;
          const r = data[idx];
          const g = data[idx + 1];
          const b = data[idx + 2];
          const a = data[idx + 3];

          // Check if pixel is NOT background (not white/transparent)
          const isBackground = (
            // Transparent pixel
            a < 50 ||
            // White or near-white pixel
            (r > 240 && g > 240 && b > 240 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15)
          );

          if (!isBackground) {
            watchPixels.push({ x, y, r, g, b, a });
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minY = Math.min(minY, y);
            maxY = Math.max(maxY, y);
          }
        }
      }

      // Calculate watch dimensions in pixels
      const watchWidthPx = maxX - minX;
      const watchHeightPx = maxY - minY;

      // Find the dial (circular/rectangular center part)
      const centerX = (minX + maxX) / 2;
      const centerY = (minY + maxY) / 2;

      // Estimate dial size by finding the densest circular area
      let dialRadiusPx = 0;
      const maxRadius = Math.min(watchWidthPx, watchHeightPx) / 3;

      for (let radius = 10; radius <= maxRadius; radius += 2) {
        let pixelsInCircle = 0;
        let totalPixelsInArea = 0;

        for (let dy = -radius; dy <= radius; dy++) {
          for (let dx = -radius; dx <= radius; dx++) {
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance <= radius) {
              totalPixelsInArea++;
              const checkX = Math.round(centerX + dx);
              const checkY = Math.round(centerY + dy);

              if (watchPixels.some(p => p.x === checkX && p.y === checkY)) {
                pixelsInCircle++;
              }
            }
          }
        }

        const density = pixelsInCircle / totalPixelsInArea;
        if (density > 0.7) { // 70% of circle area has watch pixels
          dialRadiusPx = radius;
        }
      }

      const dialDiameterPx = dialRadiusPx * 2;

      // Calculate the actual measurements
      // Use the watch height as the primary measurement for scaling
      const watchHeightMm = watchHeightPx; // This will be scaled based on SVG
      const dialDiameterMm = dialDiameterPx; // This will be scaled based on SVG

      return {
        watchWidthPx,
        watchHeightPx,
        dialDiameterPx,
        dialRadiusPx,
        watchBounds: { minX, maxX, minY, maxY },
        centerX,
        centerY,
        watchPixelCount: watchPixels.length,
        // Measurements for fitting
        watchHeightMm,
        dialDiameterMm,
        aspectRatio: watchWidthPx / watchHeightPx,
        isSegmented: true
      };
    } catch (error) {
      console.error('Error segmenting and measuring watch:', error);
      return null;
    }
  };

  // Default wrist sizes by gender (top view width in mm) - used for initial wrist size setting
  const DEFAULT_WRIST_SIZES = {
    men: 50,    // mm - average men's wrist width from top view
    women: 45   // mm - average women's wrist width from top view
  };

  // SUPER REALISTIC FITTING LOGIC using real-world measurements
  // This creates the most realistic fit by calculating optimal zoom based on case diameter vs wrist size
  const calculateWatchDimensionsFromImage = async (watch) => {
    try {
      // Segment and measure the watch image
      const measurements = await segmentAndMeasureWatch(watch.path || watch.image);

      if (measurements && measurements.isSegmented) {
        // Get real-world measurements
        const segmentedImageHeightPx = measurements.watchHeightPx; // e.g., 60px
        const segmentedImageWidthPx = measurements.watchWidthPx;   // e.g., 55px
        const caseDiameterMm = watch.dialSize || watch.caseDiameter || 44; // e.g., 44mm
        const wristSizeMm = userWristSize; // e.g., 50mm

        console.log('🔍 SUPER REALISTIC FITTING CALCULATION:');
        console.log(`📏 Segmented image height: ${segmentedImageHeightPx}px`);
        console.log(`📏 Segmented image width: ${segmentedImageWidthPx}px`);
        console.log(`⌚ Case diameter: ${caseDiameterMm}mm`);
        console.log(`🤚 Wrist size: ${wristSizeMm}mm`);

        // Show fitting explanation
        const fitDescription = caseDiameterMm / wristSizeMm >= 0.85 ?
          'Large watch on wrist - zooming in to show realistic tight fit' :
          caseDiameterMm / wristSizeMm >= 0.7 ?
          'Medium fit - moderate zoom for natural appearance' :
          'Small watch on large wrist - showing more strap for realistic look';
        console.log(`🎯 Fit type: ${fitDescription}`);

        // STEP 1: Calculate the zoom factor needed for realistic fit
        // If case diameter (44mm) is close to wrist size (50mm), we need to zoom in
        // to show mostly the case with minimal strap visible
        const caseToWristRatio = caseDiameterMm / wristSizeMm;
        console.log(`📊 Case-to-wrist ratio: ${caseToWristRatio.toFixed(2)}`);

        // STEP 2: Calculate optimal zoom level
        // When case is large relative to wrist, zoom in more to show realistic fit
        let zoomFactor;
        if (caseToWristRatio >= 0.85) {
          // Large watch on wrist - zoom in significantly (show mostly case)
          zoomFactor = 1.4 + (caseToWristRatio - 0.85) * 2;
        } else if (caseToWristRatio >= 0.7) {
          // Medium fit - moderate zoom
          zoomFactor = 1.1 + (caseToWristRatio - 0.7) * 2;
        } else {
          // Small watch on large wrist - minimal zoom (show more strap)
          zoomFactor = 0.8 + caseToWristRatio * 0.4;
        }

        // Clamp zoom factor to reasonable bounds
        zoomFactor = Math.max(0.6, Math.min(2.2, zoomFactor));
        console.log(`🔍 Calculated zoom factor: ${zoomFactor.toFixed(2)}`);

        // STEP 3: Calculate the display size based on wrist size and zoom
        // Base the size on the SVG wrist circle (120px = userWristSize mm)
        const pxToMmRatio = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;

        // Calculate how much of the segmented image to show
        const visibleImageHeightPx = segmentedImageHeightPx / zoomFactor;
        const visibleImageWidthPx = segmentedImageWidthPx / zoomFactor;

        // Convert to SVG coordinates
        const svgWatchHeight = visibleImageHeightPx * pxToMmRatio;
        const svgWatchWidth = visibleImageWidthPx * pxToMmRatio;

        // Convert to percentages for CSS
        const watchWidthPercent = (svgWatchWidth / SVG_VIEWBOX_WIDTH) * 100;
        const watchHeightPercent = (svgWatchHeight / SVG_VIEWBOX_HEIGHT) * 100;

        console.log(`📐 Final dimensions: ${watchWidthPercent.toFixed(1)}% x ${watchHeightPercent.toFixed(1)}%`);
        console.log(`🎯 Zoom effect: ${zoomFactor > 1 ? 'Zoomed in' : 'Zoomed out'} by ${Math.abs(zoomFactor - 1).toFixed(2)}x`);

        return {
          width: Math.max(watchWidthPercent, 8),
          height: Math.max(watchHeightPercent, 10),
          dialDiameter: (caseDiameterMm / userWristSize) * SVG_WRIST_CIRCLE_DIAMETER,
          positionX: 50,
          positionY: 50,
          scale: 1, // We handle scaling through zoom factor
          zoomFactor: zoomFactor,
          dialSize: caseDiameterMm,
          caseToWristRatio: caseToWristRatio,
          userWristSize: userWristSize,
          isSegmented: true,
          isRealisticFit: true,
          measurements: measurements
        };
      }
    } catch (error) {
      console.error('Error calculating dimensions from image:', error);
    }

    // Fallback to original logic if segmentation fails
    return calculateWatchDimensionsFallback(watch);
  };

  // Fallback function using dial size with enhanced realistic fitting
  const calculateWatchDimensionsFallback = (watch) => {
    // Get the watch's dial size (this is the key measurement)
    const caseDiameterMm = watch.dialSize || watch.caseDiameter || 42;
    const wristSizeMm = userWristSize;

    console.log('🔄 FALLBACK REALISTIC FITTING:');
    console.log(`⌚ Case diameter: ${caseDiameterMm}mm`);
    console.log(`🤚 Wrist size: ${wristSizeMm}mm`);

    // Apply the same realistic zoom calculation as the segmented version
    const caseToWristRatio = caseDiameterMm / wristSizeMm;

    // Calculate optimal zoom level (same logic as segmented version)
    let zoomFactor;
    if (caseToWristRatio >= 0.85) {
      zoomFactor = 1.4 + (caseToWristRatio - 0.85) * 2;
    } else if (caseToWristRatio >= 0.7) {
      zoomFactor = 1.1 + (caseToWristRatio - 0.7) * 2;
    } else {
      zoomFactor = 0.8 + caseToWristRatio * 0.4;
    }
    zoomFactor = Math.max(0.6, Math.min(2.2, zoomFactor));

    console.log(`🔍 Fallback zoom factor: ${zoomFactor.toFixed(2)}`);

    // Calculate the scale factor from real world to SVG coordinates
    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;

    // Convert watch dimensions to SVG units using user's wrist size as baseline
    const baseWatchWidthSvg = (watch.totalWidth || 42) * mmToSvgScale;
    const baseWatchHeightSvg = (watch.totalHeight || 47) * mmToSvgScale;
    const baseDialDiameterSvg = (watch.dialDiameter || caseDiameterMm) * mmToSvgScale;

    // Apply realistic scaling (inverse of zoom for display)
    const watchWidthSvg = baseWatchWidthSvg / zoomFactor;
    const watchHeightSvg = baseWatchHeightSvg / zoomFactor;
    const dialDiameterSvg = baseDialDiameterSvg;

    // Convert SVG units to container percentages
    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;
    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;
    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;

    return {
      width: Math.max(watchWidthPercent, 8),
      height: Math.max(watchHeightPercent, 10),
      dialDiameter: dialDiameterPercent,
      positionX: 50,
      positionY: 50,
      scale: 1, // We handle scaling through zoom factor
      zoomFactor: zoomFactor,
      dialSize: caseDiameterMm,
      caseToWristRatio: caseToWristRatio,
      userWristSize: userWristSize,
      isRealisticFit: true,
      isSegmented: false,
      isFallback: true
    };
  };

  // Handle gender selection
  const handleGenderChange = (gender) => {
    setUserGender(gender);
    setUserWristSize(gender === 'men' ? 50 : 45); // Set initial size based on gender
  };

  // Calculate watch position based on hand orientation and anatomy
  const getWatchPosition = (watchData, isRightHand) => {
    const baseDimensions = calculateWatchDimensionsFromImage(watchData, 400, 600);

    // Adjust position based on hand orientation
    // Watches are typically worn on the top of the wrist
    let adjustedX = baseDimensions.positionX;
    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist

    // For right hand, watch might be positioned slightly differently
    if (isRightHand) {
      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand
    }

    // Calculate scale to match SVG shape height
    const svgHeight = 300; // Height of the wrist/forearm area in SVG
    const watchHeight = watchData.totalHeight;
    const scaleToFitHeight = svgHeight / watchHeight;

    // Adjust for different watch types
    switch (watchData.type) {
      case 'smartwatch':
        // Smart watches are often worn higher on the wrist
        adjustedY -= 1;
        break;
      case 'luxury':
        // Luxury watches might be positioned more precisely
        adjustedY -= 0.5;
        break;
      case 'sport':
        // Sport watches might be worn slightly looser
        adjustedY += 0.5;
        break;
      default:
        break;
    }

    return {
      ...baseDimensions,
      positionX: adjustedX,
      positionY: adjustedY,
      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height
    };
  };

  // Realistic watch data with actual dimensions (in millimeters) - Now loaded dynamically
  // const watches = [
  //   {
  //     id: "watch_1",
  //     name: "Classic Black",
  //     path: "/imgs/watches/watch_1.png",
  //     // Rolex Submariner style - 40mm case
  //     caseDiameter: 41, // mm
  //     caseThickness: 12.5, // mm
  //     totalWidth: 42, // mm (including crown)
  //     totalHeight: 47, // mm (lug to lug)
  //     dialDiameter: 31, // mm (visible dial)
  //     type: "dress",
  //     dialSize: 40
  //   },
  //   {
  //     id: "watch_2",
  //     name: "Silver Chrono",
  //     path: "/imgs/watches/watch_2.png",
  //     // Omega Speedmaster style - 42mm case
  //     caseDiameter: 42, // mm
  //     caseThickness: 13.2, // mm
  //     totalWidth: 44, // mm
  //     totalHeight: 48.5, // mm
  //     dialDiameter: 33, // mm
  //     type: "sport",
  //     dialSize: 42
  //   },
  //   {
  //     id: "watch_3",
  //     name: "Gold Luxury",
  //     path: "/imgs/watches/watch_3.png",
  //     // Patek Philippe Calatrava style - 38mm case
  //     caseDiameter: 39, // mm
  //     caseThickness: 8.5, // mm
  //     totalWidth: 39, // mm
  //     totalHeight: 45, // mm
  //     dialDiameter: 30, // mm
  //     type: "luxury",
  //     dialSize: 38
  //   },
  //   {
  //     id: "watch_6",
  //     name: "Sport Blue",
  //     path: "/imgs/watches/watch_6.png",
  //     // Apple Watch style - 44mm case
  //     caseDiameter: 41, // mm (width)
  //     caseThickness: 10.7, // mm
  //     totalWidth: 44, // mm
  //     totalHeight: 38, // mm (height - rectangular)
  //     dialDiameter: 35, // mm (screen diagonal)
  //     type: "smartwatch",
  //     dialSize: 44
  //   },
  //   {
  //     id: "watch_5",
  //     name: "Minimalist",
  //     path: "/imgs/watches/watch_5.png",
  //     // Daniel Wellington style - 36mm case
  //     caseDiameter: 36, // mm
  //     caseThickness: 6, // mm
  //     totalWidth: 37, // mm
  //     totalHeight: 43, // mm
  //     dialDiameter: 28, // mm
  //     type: "minimalist",
  //     dialSize: 36
  //   },
  //   {
  //     id: "watch_4",
  //     name: "Rose Gold",
  //     path: "/imgs/watches/watch_4.png",
  //     // Michael Kors style - 39mm case
  //     caseDiameter: 44, // mm
  //     caseThickness: 11, // mm
  //     totalWidth: 41, // mm
  //     totalHeight: 46, // mm
  //     dialDiameter: 31, // mm
  //     type: "fashion",
  //     dialSize: 41
  //   }
  // ];

  const bracelets = [
    { id: "bracelet_1", name: "Silver Chain", path: "/imgs/bracelets_tryon/bracelet_1.png" },
    { id: "bracelet_2", name: "Gold Bangle", path: "/imgs/bracelets_tryon/bracelet_2.png" },
    { id: "bracelet_3", name: "Leather Wrap", path: "/imgs/bracelets_tryon/bracelet_3.png" },
    { id: "bracelet_4", name: "Diamond Tennis", path: "/imgs/bracelets_tryon/bracelet_4.png" },
    { id: "bracelet_5", name: "Beaded Stone", path: "/imgs/bracelets_tryon/bracelet_5.png" },
    { id: "bracelet_6", name: "Charm Bracelet", path: "/imgs/bracelets_tryon/bracelet_6.png" }
  ];

  const rings = [
    { id: "ring_1", name: "Diamond Solitaire", path: "/imgs/rings_tryon/rings01.jpg" },
    { id: "ring_2", name: "Gold Band", path: "/imgs/rings_tryon/rings02.jpg" },
    { id: "ring_3", name: "Silver Ring", path: "/imgs/rings_tryon/rings03.jpg" },
    { id: "ring_4", name: "Gemstone Ring", path: "/imgs/rings_tryon/rings04.jpg" },
    { id: "ring_5", name: "Wedding Band", path: "/imgs/rings_tryon/rings05.jpg" }
  ];

  const earrings = [
    { id: "earring_1", name: "Diamond Studs", path: "/imgs/earrings_tryon/rings01.jpg" },
    { id: "earring_2", name: "Gold Hoops", path: "/imgs/earrings_tryon/rings02.jpg" },
    { id: "earring_3", name: "Pearl Drops", path: "/imgs/earrings_tryon/rings03.jpg" },
    { id: "earring_4", name: "Silver Earrings", path: "/imgs/earrings_tryon/rings04.jpg" },
    { id: "earring_5", name: "Gemstone Earrings", path: "/imgs/earrings_tryon/rings05.jpg" }
  ];

  // Initialize camera
  const initCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      // Demo mode fallback
      if (capturedImageRef.current) {
        capturedImageRef.current.src = "sample-hand.jpg";
        capturedImageRef.current.style.display = "block";
      }
      console.log("Camera not available - demo mode");
      setIsCaptured(true);
      setShowProductSelection(true);
    }
  };

  // Capture current frame
  const captureFrame = () => {
    if (!videoRef.current || !canvasRef.current) return null;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
  };

  // Advanced background remover with intelligent detection
  const removeBackground = (imgElement, productType = 'watch') => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = function() {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      ctx.drawImage(img, 0, 0);

      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const width = canvas.width;
        const height = canvas.height;

        // Step 1: Check if image already has transparent background
        let hasTransparentPixels = false;
        let transparentPixelCount = 0;
        for (let i = 3; i < data.length; i += 4) {
          if (data[i] < 255) {
            hasTransparentPixels = true;
            if (data[i] === 0) transparentPixelCount++;
          }
        }

        // If image already has significant transparency, skip background removal
        const transparencyRatio = transparentPixelCount / (width * height);
        if (transparencyRatio > 0.1) {
          console.log('Image already has transparent background, skipping removal');
          return;
        }

        // Step 2: Analyze color distribution to identify background
        const colorHistogram = new Map();
        const edgePixels = new Set();

        // Build color histogram and detect edges
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const idx = (y * width + x) * 4;
            const r = data[idx];
            const g = data[idx + 1];
            const b = data[idx + 2];
            const a = data[idx + 3];

            if (a === 0) continue; // Skip already transparent pixels

            // Quantize colors for histogram (reduce precision for grouping)
            const colorKey = `${Math.floor(r/8)*8},${Math.floor(g/8)*8},${Math.floor(b/8)*8}`;
            colorHistogram.set(colorKey, (colorHistogram.get(colorKey) || 0) + 1);

            // Edge detection using Sobel-like operator
            if (x > 0 && x < width - 1 && y > 0 && y < height - 1) {
              let gradientX = 0, gradientY = 0;

              // Calculate gradients
              for (let dy = -1; dy <= 1; dy++) {
                for (let dx = -1; dx <= 1; dx++) {
                  const nIdx = ((y + dy) * width + (x + dx)) * 4;
                  const intensity = (data[nIdx] + data[nIdx + 1] + data[nIdx + 2]) / 3;

                  // Sobel X kernel: [-1,0,1; -2,0,2; -1,0,1]
                  if (dx === -1) gradientX -= intensity * (dy === 0 ? 2 : 1);
                  if (dx === 1) gradientX += intensity * (dy === 0 ? 2 : 1);

                  // Sobel Y kernel: [-1,-2,-1; 0,0,0; 1,2,1]
                  if (dy === -1) gradientY -= intensity * (dx === 0 ? 2 : 1);
                  if (dy === 1) gradientY += intensity * (dx === 0 ? 2 : 1);
                }
              }

              const magnitude = Math.sqrt(gradientX * gradientX + gradientY * gradientY);
              if (magnitude > 30) {
                edgePixels.add(y * width + x);
              }
            }
          }
        }

        // Step 3: Identify background colors (most common colors near edges)
        const backgroundColors = new Set();
        const sortedColors = Array.from(colorHistogram.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5); // Top 5 most common colors

        // Check edge pixels to identify likely background colors
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            // Check border pixels (likely background)
            if (x < 5 || x >= width - 5 || y < 5 || y >= height - 5) {
              const idx = (y * width + x) * 4;
              const r = data[idx];
              const g = data[idx + 1];
              const b = data[idx + 2];
              const colorKey = `${Math.floor(r/8)*8},${Math.floor(g/8)*8},${Math.floor(b/8)*8}`;

              // If this color is common and near borders, it's likely background
              if (colorHistogram.get(colorKey) > width * height * 0.01) {
                backgroundColors.add(colorKey);
              }
            }
          }
        }

        // Step 4: Advanced background removal using flood fill and color similarity
        const visited = new Set();
        const isBackgroundPixel = (x, y) => {
          const idx = (y * width + x) * 4;
          const r = data[idx];
          const g = data[idx + 1];
          const b = data[idx + 2];
          const colorKey = `${Math.floor(r/8)*8},${Math.floor(g/8)*8},${Math.floor(b/8)*8}`;

          // Check if it's a known background color
          if (backgroundColors.has(colorKey)) return true;

          // Check if it's very similar to white/light gray (but more sophisticated)
          const brightness = (r + g + b) / 3;
          const saturation = Math.max(r, g, b) - Math.min(r, g, b);

          return brightness > 240 && saturation < 15 && !edgePixels.has(y * width + x);
        };

        // Flood fill from border pixels to remove connected background regions
        const floodFill = (startX, startY) => {
          const stack = [[startX, startY]];
          const region = [];

          while (stack.length > 0) {
            const [x, y] = stack.pop();
            const key = `${x},${y}`;

            if (visited.has(key) || x < 0 || x >= width || y < 0 || y >= height) continue;
            if (!isBackgroundPixel(x, y)) continue;

            visited.add(key);
            region.push([x, y]);

            // Add neighbors
            stack.push([x+1, y], [x-1, y], [x, y+1], [x, y-1]);
          }

          return region;
        };

        // Start flood fill from border pixels
        for (let x = 0; x < width; x++) {
          for (let y of [0, height - 1]) {
            if (!visited.has(`${x},${y}`) && isBackgroundPixel(x, y)) {
              const region = floodFill(x, y);
              // Remove this background region
              for (const [px, py] of region) {
                const idx = (py * width + px) * 4;
                data[idx + 3] = 0; // Make transparent
              }
            }
          }
        }

        for (let y = 0; y < height; y++) {
          for (let x of [0, width - 1]) {
            if (!visited.has(`${x},${y}`) && isBackgroundPixel(x, y)) {
              const region = floodFill(x, y);
              // Remove this background region
              for (const [px, py] of region) {
                const idx = (py * width + px) * 4;
                data[idx + 3] = 0; // Make transparent
              }
            }
          }
        }

        ctx.putImageData(imageData, 0, 0);
        imgElement.src = canvas.toDataURL('image/png');

        // Apply clean styling
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';

      } catch (e) {
        console.warn('Advanced background removal failed:', e);
        // Fallback styling that preserves all colors
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';
      }
    };

    img.onerror = function() {
      console.warn('Image loading failed');
      // Fallback styling
      imgElement.style.filter = 'none';
      imgElement.style.mixBlendMode = 'normal';
      imgElement.style.opacity = '1';
    };

    img.src = imgElement.src;
  };

  // Placeholder for hand detection
  const detectHandOrientation = (imageData) => {
    // Simple heuristic for demo purposes
    return Math.random() > 0.5;
  };

  // Detect if arm and wrist are within the SVG guide area
  const detectHandInPosition = () => {
    if (!videoRef.current || !canvasRef.current) return false;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get the video container dimensions to calculate the guide area
    const videoContainer = video.parentElement;
    const containerRect = videoContainer.getBoundingClientRect();

    // Calculate the actual video display area (accounting for object-fit: cover)
    const videoAspect = video.videoWidth / video.videoHeight;
    const containerAspect = containerRect.width / containerRect.height;

    let displayWidth, displayHeight, offsetX, offsetY;

    if (videoAspect > containerAspect) {
      // Video is wider - height fills container, width is cropped
      displayHeight = containerRect.height;
      displayWidth = displayHeight * videoAspect;
      offsetX = (displayWidth - containerRect.width) / 2;
      offsetY = 0;
    } else {
      // Video is taller - width fills container, height is cropped
      displayWidth = containerRect.width;
      displayHeight = displayWidth / videoAspect;
      offsetX = 0;
      offsetY = (displayHeight - containerRect.height) / 2;
    }

    // Calculate the guide areas in canvas coordinates
    // SVG viewBox is 800x600
    // Main rectangle: x="320" y="150" width="160" height="300" (wrist/forearm area)
    // Circle: cx="400" cy="300" r="60" (hand area)

    const scaleX = canvas.width / displayWidth;
    const scaleY = canvas.height / displayHeight;

    // Wrist/forearm area (rectangle)
    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);
    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);
    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);
    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);

    // Hand area (circle)
    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds
    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds
    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter
    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter

    try {
      // Check wrist/forearm area (rectangle)
      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);
      const rectData = rectImageData.data;

      // Check hand area (circle approximation)
      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);
      const circleData = circleImageData.data;

      let rectSkinPixels = 0;
      let rectTotalPixels = 0;
      let circleSkinPixels = 0;
      let circleTotalPixels = 0;

      // Enhanced skin tone detection
      const isSkinTone = (r, g, b) => {
        // Multiple skin tone ranges to cover different skin colors
        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;
        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;
        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;
        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;

        return condition1 || condition2 || condition3 || condition4;
      };

      // Analyze rectangle area (wrist/forearm)
      for (let i = 0; i < rectData.length; i += 4) {
        const r = rectData[i];
        const g = rectData[i + 1];
        const b = rectData[i + 2];

        if (isSkinTone(r, g, b)) {
          rectSkinPixels++;
        }
        rectTotalPixels++;
      }

      // Analyze circle area (hand)
      for (let i = 0; i < circleData.length; i += 4) {
        const r = circleData[i];
        const g = circleData[i + 1];
        const b = circleData[i + 2];

        if (isSkinTone(r, g, b)) {
          circleSkinPixels++;
        }
        circleTotalPixels++;
      }

      // Calculate skin ratios
      const rectSkinRatio = rectSkinPixels / rectTotalPixels;
      const circleSkinRatio = circleSkinPixels / circleTotalPixels;

      // Both wrist/forearm AND hand areas must have sufficient skin tone presence
      // Wrist area needs at least 20% skin tone (forearm/wrist)
      // Hand area needs at least 25% skin tone (hand/fingers)
      const wristInPosition = rectSkinRatio > 0.20;
      const handInPosition = circleSkinRatio > 0.25;

      return wristInPosition && handInPosition;

    } catch (error) {
      console.warn('Hand detection error:', error);
      return false;
    }
  };

  // Apply product to watch position with dynamic scaling and image resizing
  const applyProductToWatchPosition = async (productPath, productType) => {
    // Clear any existing product first
    setSelectedProduct(null);

    // Find the watch data for dial size
    const watchData = watches.find(w => w.path === productPath);

    try {
      // Resize the image with proper padding for consistent fitting
      const resizedImageBlob = await resizeImageWithPadding(productPath);
      let finalPath = productPath;

      if (resizedImageBlob) {
        // Create a URL for the resized image
        finalPath = URL.createObjectURL(resizedImageBlob);
      }

      // Small delay to ensure the old product is removed before adding new one
      setTimeout(() => {
        setSelectedProduct({
          path: finalPath,
          originalPath: productPath, // Keep original path for reference
          dialSize: watchData?.dialSize || 40, // Default to 40mm if not found
          dimensions: watchData, // Pass full watch dimensions for scaling
          isResized: !!resizedImageBlob // Flag to track if image was resized
        });
      }, 50);
    } catch (error) {
      console.error('Error resizing image:', error);
      // Fallback to original image if resizing fails
      setTimeout(() => {
        setSelectedProduct({
          path: productPath,
          dialSize: watchData?.dialSize || 40,
          dimensions: watchData
        });
      }, 50);
    }
  };

  // Handle capture button click
  const handleCapture = () => {
    if (!isCaptured) {
      const capturedDataUrl = captureFrame();
      if (capturedImageRef.current && capturedDataUrl) {
        capturedImageRef.current.src = capturedDataUrl;
        capturedImageRef.current.style.display = 'block';
      }
      setIsCaptured(true);
      setIsUsingModelImage(false);
      setShowProductSelection(true); // Show products immediately
      setShowHandGuide(false);

      // Detect hand orientation
      if (canvasRef.current && videoRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        canvas.width = videoRef.current.videoWidth;
        canvas.height = videoRef.current.videoHeight;
        ctx.drawImage(videoRef.current, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        setIsRightHand(detectHandOrientation(imageData));
      }
    } else {
      setShowProductSelection(true);
    }
  };

  // Handle back button click
  const handleBack = () => {
    if (capturedImageRef.current) {
      capturedImageRef.current.style.display = 'none';
    }
    setIsCaptured(false);
    setSelectedProduct(null);
    setSelectedWatch(null); // Reset stacking state
    setSelectedBracelet(null); // Reset stacking state
    setIsStackingSwapped(false); // Reset stacking swap state
    setIsRightHand(false);
    setShowProductSelection(false);
    setShowWristSizeModal(false);
    setShowHandGuide(true);
    setIsUsingModelImage(false); // Reset model image state
  };

  // Handle wrist size change
  const handleWristSizeChange = (size) => {
    setUserWristSize(size);
  };

  // Handle continue to product selection - No longer needed since both panels show together
  // const handleContinueToProducts = () => {
  //   setShowWristSizeInput(false);
  //   setShowProductSelection(true);
  // };

  // Handle tab change
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);

    // Only reset selections when completely leaving stacking mode
    if (tabName !== 'Stacking' && tabName !== 'Watches' && tabName !== 'Bracelets') {
      setSelectedWatch(null);
      setSelectedBracelet(null);
      setIsStackingSwapped(false); // Reset swap state when leaving stacking
      setSelectedProduct(null);
    } else if (tabName === 'Stacking') {
      setSelectedProduct(null);
    }
  };

  // Handle product selection
  const handleProductSelect = async (product) => {
    if (activeTab === 'Stacking') {
      // For stacking, determine if it's a watch or bracelet and set accordingly
      const isWatch = watches.some(w => w.id === product.id);
      const isBracelet = bracelets.some(b => b.id === product.id);

      try {
        // Resize the image for consistent fitting
        const resizedImageBlob = await resizeImageWithPadding(product.path);
        let resizedProduct = { ...product };

        if (resizedImageBlob) {
          resizedProduct.path = URL.createObjectURL(resizedImageBlob);
          resizedProduct.originalPath = product.path;
          resizedProduct.isResized = true;
        }

        if (isWatch) {
          setSelectedWatch(resizedProduct);
        } else if (isBracelet) {
          setSelectedBracelet(resizedProduct);
        }
      } catch (error) {
        console.error('Error resizing stacking image:', error);
        // Fallback to original product if resizing fails
        if (isWatch) {
          setSelectedWatch(product);
        } else if (isBracelet) {
          setSelectedBracelet(product);
        }
      }
    } else {
      // For regular tabs, use the existing logic
      applyProductToWatchPosition(product.path, activeTab);
    }
  };

  // Handle Try On Model button click
  const handleTryOnModel = () => {
    // Load the model hand image from public folder
    const modelImagePath = '/imgs/hand/hand.png';
    if (capturedImageRef.current) {
      capturedImageRef.current.src = modelImagePath;
      capturedImageRef.current.style.display = 'block';
      capturedImageRef.current.onload = () => {
        detectWristOnModelImage();
      };
    }
    setIsCaptured(true);
    setIsUsingModelImage(true);
    setShowProductSelection(true);
    setShowHandGuide(false);
    setIsRightHand(false);
  };

  // Detect wrist position on model image and apply scaling
  const detectWristOnModelImage = () => {
    if (!capturedImageRef.current) return;

    try {
      // Create a canvas to analyze the model image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = capturedImageRef.current;

      canvas.width = img.naturalWidth || 800;
      canvas.height = img.naturalHeight || 600;
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // For model image with closed palm, position the watch/bracelet on the palm area
      // Since it's a closed hand, we need to detect the palm center rather than wrist
      const palmPosition = detectPalmPositionOnModel(canvas);

      if (palmPosition) {
        console.log('Palm position detected on model image:', palmPosition);
        // Store the palm position for product placement
        window.modelPalmPosition = palmPosition;
      } else {
        console.warn('Could not detect palm position on model image');
      }

    } catch (error) {
      console.warn('Model image palm detection error:', error);
    }
  };

  // Detect palm position on closed hand model image
  const detectPalmPositionOnModel = (canvas) => {
    try {
      const ctx = canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // For a closed hand model, we need to find the wrist area where the watch/bracelet should go
      // The wrist is typically at the bottom of the hand, where the arm connects

      // Scan from bottom up to find the wrist area (narrowest part of the arm)
      let wristY = null;
      let wristX = canvas.width * 0.5; // Start from center
      let minWidth = canvas.width;

      // Scan from bottom 20% to 80% of image height
      for (let y = Math.floor(canvas.height * 0.8); y > canvas.height * 0.2; y -= 5) {
        let leftEdge = -1;
        let rightEdge = -1;

        // Find left and right edges of the arm/hand at this height
        for (let x = 0; x < canvas.width; x++) {
          const idx = (y * canvas.width + x) * 4;
          const r = data[idx];
          const g = data[idx + 1];
          const b = data[idx + 2];
          const alpha = data[idx + 3];

          // Check if this is a skin-colored pixel (not background)
          if (alpha > 128 && (r > 100 || g > 80 || b > 60)) {
            if (leftEdge === -1) leftEdge = x;
            rightEdge = x;
          }
        }

        if (leftEdge !== -1 && rightEdge !== -1) {
          const width = rightEdge - leftEdge;
          if (width < minWidth && width > 20) { // Must be reasonable width
            minWidth = width;
            wristY = y;
            wristX = (leftEdge + rightEdge) / 2;
          }
        }
      }

      if (wristY !== null) {
        // Found wrist position - return bigger area for model hand
        return {
          x: wristX - 80,
          y: wristY - 40,
          width: 160,
          height: 80,
          centerX: wristX,
          centerY: wristY + 10, // Slightly below wrist center
          confidence: 0.8,
          isModelHand: true
        };
      }

      return null;

    } catch (error) {
      console.error('Error detecting wrist position on closed hand:', error);
      // Return default wrist position with bigger size for model hand
      return {
        x: canvas.width * 0.5 - 80,
        y: canvas.height * 0.85 - 40,
        width: 160,
        height: 80,
        centerX: canvas.width * 0.5,
        centerY: canvas.height * 0.85 + 10,
        confidence: 0.7,
        isModelHand: true
      };
    }
  };

  // Handle disclaimer popup close
  const handleDisclaimerClose = () => {
    setShowDisclaimerPopup(false);
  };

  // Initialize camera and realistic try-on on component mount
  useEffect(() => {
    const initializeSystem = async () => {
      await initCamera();

      // Initialize realistic try-on system
      if (videoRef.current && canvasRef.current) {
        try {
          const initialized = await realisticTryOn.initRealisticTryOn(
            videoRef.current,
            canvasRef.current,
            handleRealisticTryOnUpdate,
            {
              enableDebug: false,
              targetFrameRate: 30,
              enableOpenCV: true
            }
          );
          setRealisticTryOnInitialized(initialized);

          if (initialized) {
            console.log('Realistic try-on system initialized successfully');
          } else {
            console.warn('Failed to initialize realistic try-on system');
          }
        } catch (error) {
          console.error('Error initializing realistic try-on:', error);
        }
      }
    };

    initializeSystem();
    // Show disclaimer popup when component first loads
    setShowDisclaimerPopup(true);

    // Cleanup function to stop realistic try-on system when component unmounts
    return () => {
      if (realisticTryOnInitialized) {
        realisticTryOn.stopRealisticTryOn()
          .then(() => console.log('Realistic try-on system stopped'))
          .catch(error => console.error('Error stopping realistic try-on system:', error));
      }
    };
  }, [realisticTryOnInitialized]);

  // Handle realistic try-on data updates
  const handleRealisticTryOnUpdate = (data) => {
    const {
      handData: newHandData,
      handPose: newHandPose,
      realWorldMeasurements: newRealWorldMeasurements,
      transforms: newTransforms,
      performanceMetrics: newPerformanceMetrics
    } = data;

    setHandData(newHandData);
    setHandPose(newHandPose);
    setRealWorldMeasurements(newRealWorldMeasurements);
    setPostureTransforms(newTransforms);
    setPerformanceMetrics(newPerformanceMetrics);
  };

  // Hand position monitoring effect (only when autocapture is enabled)
  useEffect(() => {
    if (!isAutoCaptureEnabled || isCaptured) return;

    const interval = setInterval(() => {
      const handInPosition = detectHandInPosition();
      setIsHandInPosition(handInPosition);

      // Only start countdown if hand is properly positioned
      if (handInPosition && !isCountdownActive && countdown === 0) {
        setIsCountdownActive(true);
      }

      // Stop countdown if hand moves out of position
      if (!handInPosition && isCountdownActive) {
        setIsCountdownActive(false);
        setCountdown(0);
      }
    }, 150); // Check every 150ms for better performance

    return () => clearInterval(interval);
  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);

  // Countdown effect (starts automatically when hand is in position)
  useEffect(() => {
    if (!isCountdownActive || isCaptured) {
      setCountdown(0);
      return;
    }

    // Only start countdown if hand is still in position
    if (!isHandInPosition) {
      setIsCountdownActive(false);
      setCountdown(0);
      return;
    }

    // Start countdown
    setCountdown(3);

    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        // Double-check hand position before continuing countdown
        if (!isHandInPosition) {
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          return 0;
        }

        if (prev <= 1) {
          // Countdown finished - trigger capture
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture
          handleCapture();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, [isCountdownActive, isCaptured, isHandInPosition]);

  // Handle autocapture switch toggle
  const handleAutoCaptureToggle = () => {
    const newState = !isAutoCaptureEnabled;
    setIsAutoCaptureEnabled(newState);

    // Reset states when turning off
    if (!newState) {
      setIsCountdownActive(false);
      setCountdown(0);
      setIsHandInPosition(false);
    }
  };

  // Reset autocapture when going back
  const handleBackWithReset = () => {
    setIsAutoCaptureEnabled(false);
    setIsCountdownActive(false);
    setCountdown(0);
    setIsHandInPosition(false);
    setShowWristSizeModal(false);
    setUserWristSize(50); // Default to men's size
    setIsUsingModelImage(false); // Reset model image state
    // Go back to home instead of just resetting
    onBackToHome();
  };

  // Image resizing function to ensure consistent fitting - revert to old logic but increase height
  const resizeImageWithPadding = (imageSrc, targetWidth = 512, targetHeight = 512) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = async () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = targetWidth;
        canvas.height = targetHeight;

        // Fill with white background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, targetWidth, targetHeight);

        // REALISTIC FITTING: Use zoom factor if available from segmentation
        let scale = Math.min(targetWidth / img.width, targetHeight / img.height) * 0.9;

        // Check if we have realistic fitting data for this product
        if (selectedProduct && selectedProduct.isRealisticFit && selectedProduct.zoomFactor) {
          // Apply the calculated zoom factor for super realistic fit
          scale = scale * selectedProduct.zoomFactor;
          console.log(`🎯 Applying realistic zoom: ${selectedProduct.zoomFactor.toFixed(2)}x`);
          console.log(`📏 Case-to-wrist ratio: ${selectedProduct.caseToWristRatio?.toFixed(2)}`);
        }

        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        // Center the image
        const x = (targetWidth - scaledWidth) / 2;
        const y = (targetHeight - scaledHeight) / 2;

        // Draw the image
        ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

        // Convert to blob and resolve
        canvas.toBlob(resolve, 'image/png', 0.9);
      };

      img.onerror = () => {
        console.error('Failed to load image for resizing:', imageSrc);
        resolve(null);
      };

      img.src = imageSrc;
    });
  };

  // Get current products based on active tab
  const getCurrentProducts = () => {
    switch (activeTab) {
      case 'Watches':
        return watches;
      case 'Bracelets':
        return bracelets;
      case 'Rings':
        return rings;
      case 'Earrings':
        return earrings;
      case 'Stacking':
        return [...watches, ...bracelets]; // Combine watches and bracelets for stacking
      default:
        return watches;
    }
  };

  // Add touch gesture handlers
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    
    const currentY = e.touches[0].clientY;
    const diff = currentY - startY;
    
    // Only allow dragging down
    if (diff > 0) {
      setPanelPosition(diff);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // If dragged more than 100px down, close the panel
    if (panelPosition > 100) {
      setShowProductSelection(false);
    }
    
    // Reset position
    setPanelPosition(0);
  };

  // Add click outside handler for modals
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (showWristSizeModal && !e.target.closest('.modal-content')) {
        setShowWristSizeModal(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showWristSizeModal]);

  // Desktop QR Code Component
  const DesktopQRCode = () => (
    <div style={styles.desktopContainer}>
      <div style={styles.qrContainer}>
        <h2 style={styles.qrTitle}>Scan QR Code to Try On</h2>
        <p style={styles.qrSubtitle}>Open this page on your mobile device to experience the virtual try-on feature</p>
        <div style={styles.qrWrapper}>
          <QRCodeSVG
            value="https://www.viatryon.com/try-on/watches"
            size={256}
            level="H"
            includeMargin={true}
          />
        </div>
        <p style={styles.qrLink}>https://www.viatryon.com/try-on/watches</p>
        <button
          style={styles.homeBtn}
          onClick={onBackToHome}
          aria-label="Home"
        >
          ← Back to Home
        </button>
      </div>
    </div>
  );

  // Return desktop view if not on mobile
  if (isDesktop) {
    return <DesktopQRCode />;
  }

  // Update product selection panel JSX
  return (
    <div style={styles.container}>
      {/* Tangiblee-Style Interface */}
      {realisticTryOnInitialized && showTangibleeInterface && (
        <TangibleeInterface
          handData={handData}
          realWorldMeasurements={realWorldMeasurements}
          watchSpecs={selectedProduct}
          isVisible={isCaptured}
          onMeasurementUpdate={(measurements) => {
            console.log('Measurement update:', measurements);
          }}
        />
      )}
      {/* Disclaimer Popup */}
      {showDisclaimerPopup && (
        <div style={styles.disclaimerOverlay}>
          <div style={styles.disclaimerPopup}>
            <div style={styles.disclaimerContent}>
              <h3 style={styles.disclaimerTitle}>Before You Start</h3>
              <div style={styles.disclaimerPoints}>
                <p style={styles.disclaimerPoint}>
                  Images shown are for demonstration purposes - actual size may vary
                </p>
                <p style={styles.disclaimerPoint}>
                  Position your wrist within the guide lines for better results
                </p>
              </div>
              <button
                style={styles.disclaimerButton}
                onClick={handleDisclaimerClose}
              >
                Got it
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Back Arrow Button - Top Left */}
      <a
        href="/"
        style={styles.backArrowBtn}
        aria-label="Back to Home"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
        </svg>
      </a>

      {/* Powered by ViaTryon Branding - Between back arrow and autocapture */}
      <div style={styles.brandingContainerBetween}>
        <img
          src="/imgs/logo-only.png"
          alt="ViaTryon Logo"
          style={{
            ...styles.brandingLogoTiny,
            filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
          }}
        />
        <div style={styles.brandingTextTiny}>
          <span style={{
            ...styles.poweredByTextTiny,
            color: isUsingModelImage ? '#000000' : styles.poweredByTextTiny.color
          }}>Powered by</span>
          <span style={{
            ...styles.viatryonTextTiny,
            color: isUsingModelImage ? '#000000' : styles.viatryonTextTiny.color
          }}>ViaTryon</span>
        </div>
      </div>

      <div style={styles.cameraContainer}>
        <video
          ref={videoRef}
          style={styles.cameraFeed}
          autoPlay
          playsInline
          muted
        />
        <canvas ref={canvasRef} style={{ display: 'none' }} />
        <img
          ref={capturedImageRef}
          style={styles.capturedImage}
          alt="Captured hand"
        />

        {/* Autocapture Switch Button - Only visible when not captured */}
        {!isCaptured && (
          <div style={{
            position: 'absolute',
            top: isMobile ? 10 : 20,
            right: isMobile ? 10 : 20,
            zIndex: 20,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px',
            padding: '12px',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            borderRadius: '20px',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <label className="switch-label">
              Auto Capture
            </label>
            <label className="switch-container">
              <input
                type="checkbox"
                checked={isAutoCaptureEnabled}
                onChange={handleAutoCaptureToggle}
                disabled={isCountdownActive}
                aria-label="Toggle auto capture"
              />
              <span className="switch-slider"></span>
            </label>
          </div>
        )}



        {/* Countdown Display - Only visible during active countdown */}
        {isCountdownActive && (
          <div style={styles.countdownDisplay}>
            <div style={styles.countdownNumber}>{countdown}</div>
            <div style={styles.countdownText}>Auto capturing...</div>
          </div>
        )}

        {/* Simple Instruction Message - Clean and minimal */}
        {!isCaptured && !isCountdownActive && !isAutoCaptureEnabled && (
          <div style={styles.instructionContainer}>
            <div style={styles.instructionText}>
              Position your wrist within the guides
            </div>
          </div>
        )}

        {/* Status Messages */}
        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessageSmall}>
            <div style={styles.statusTextSmall}>Position your arm and wrist in the guide area</div>
            <div style={styles.statusSubtextSmall}>Countdown will start automatically when detected</div>
          </div>
        )}

        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessage}>
            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>
              Perfect! Starting countdown...
            </div>
          </div>
        )}

        {/* Stacking Position Switch - Small circular button above SVG */}
        {activeTab === 'Stacking' && selectedWatch && selectedBracelet && isCaptured && (
          <button
            style={styles.stackingSwitchButton}
            onClick={() => setIsStackingSwapped(!isStackingSwapped)}
            aria-label={`Switch to ${isStackingSwapped ? 'Bracelet Left' : 'Watch Left'}`}
            title={`Switch to ${isStackingSwapped ? 'Bracelet Left' : 'Watch Left'}`}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
              <path d="M6.99 11L3 15L6.99 19L8.41 17.58L6.83 16H21V14H6.83L8.41 12.42L6.99 11ZM21 9L17.01 5L15.59 6.42L17.17 8H3V10H17.17L15.59 11.58L17.01 13L21 9Z"/>
            </svg>
          </button>
        )}

        {/* Hand Guide SVG */}
        {showHandGuide && (
          <div
            style={{
              ...styles.handGuide,
              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,
              filter: isAutoCaptureEnabled && isHandInPosition
                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'
                : isAutoCaptureEnabled && !isHandInPosition
                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'
                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'
            }}
            className={isMobile ? 'mobile-hand-guide' : ''}
            aria-hidden="true"
          >
            <svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
              {/* Wrist guide lines */}
              <path
                d="M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              <path
                d="M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              {/* Wrist/Forearm area (rectangle) */}
              <rect
                x="320"
                y="150"
                width="160"
                height="300"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.25" : "0.15"}
                rx="15"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Hand area (circle) */}
              <circle
                cx="400"
                cy="300"
                r="60"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.3" : "0.2"}
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Labels for clarity */}
              {isAutoCaptureEnabled && (
                <>
                  <text x="400" y="140" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    WRIST & FOREARM
                  </text>
                  <text x="400" y="480" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    HAND
                  </text>
                </>
              )}
            </svg>
          </div>
        )}

        {/* Product Display - Only show after capture */}
        {((selectedProduct && isCaptured) || (activeTab === 'Stacking' && (selectedWatch || selectedBracelet) && isCaptured)) && (
          <>
            {/* Regular product display for non-stacking tabs */}
            {selectedProduct && activeTab !== 'Stacking' && (
              <div style={{
                ...styles.productPosition,
                // Adjust positioning for model hand - move up/down and make bigger
                // Change '52%' to move up (lower %) or down (higher %)
                top: isUsingModelImage ? '52%' : '50%',
                transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',
            width: (() => {
              switch (activeTab) {
                case 'Watches':
                  return `${WATCH_WIDTH}%`;
                case 'Bracelets':
                  return `${BRACELET_WIDTH}%`;
                case 'Rings':
                  return `${RING_WIDTH}%`;
                case 'Earrings':
                  return `${EARRING_WIDTH}%`;
                default:
                  return `${WATCH_WIDTH}%`;
              }
            })(),
            height: (() => {
              switch (activeTab) {
                case 'Watches':
                  return (() => {
                    const defaultWristSize = DEFAULT_WRIST_SIZE;
                    const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);

                    if (isLargeWrist) {
                      // For large wrists, increase height by 40% to allow exceeding SVG height
                      const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;
                      return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;
                    }
                    return `${WATCH_HEIGHT}%`;
                  })();
                case 'Bracelets':
                  return `${BRACELET_HEIGHT}%`;
                case 'Rings':
                  return `${RING_HEIGHT}%`;
                case 'Earrings':
                  return `${EARRING_HEIGHT}%`;
                default:
                  return `${WATCH_HEIGHT}%`;
              }
            })(),
            // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)
            clipPath: (() => {
              const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);
              return activeTab === 'Watches' && isLargeWrist
                ? 'ellipse(220px 60px at 50% 50%)'
                : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE
                  ? 'ellipse(220px 60px at 50% 50%)'
                  : 'none';
            })(),
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <img
                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}
                alt="Selected product"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  transform: (() => {
                    let baseTransform = '';

                    if (activeTab === 'Bracelets') {
                      // Use exact bracelet fitting logic with hand-based vertical rotation
                      baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 35})`;

                      // Apply realistic bracelet positioning based on hand detection
                      // Rotate vertically but in opposite directions for left vs right hand
                      if (isUsingModelImage) {
                        // For left hand model: rotate vertically in opposite direction
                        baseTransform = `${baseTransform} scaleY(1)`; // Opposite of current scaleY(-1)
                      } else if (isRightHand) {
                        // For right hand: rotate vertically one direction
                        baseTransform = `${baseTransform} scaleY(-1)`;
                      } else {
                        // For left hand: rotate vertically opposite direction
                        baseTransform = `${baseTransform} scaleY(1)`;
                      }
                    } else if (activeTab === 'Rings') {
                      baseTransform = `scale(${RING_HEIGHT / 25})`;
                    } else if (activeTab === 'Earrings') {
                      baseTransform = `scale(${EARRING_HEIGHT / 25})`;
                    } else {
                      // Watches - use SUPER REALISTIC fitting logic with zoom factor
                      if (selectedProduct && selectedProduct.isRealisticFit && selectedProduct.zoomFactor) {
                        // Use the calculated zoom factor for super realistic display
                        const baseScale = WATCH_HEIGHT / 25;
                        const realisticScale = baseScale * (1 / selectedProduct.zoomFactor); // Inverse because zoom is applied in image processing
                        console.log(`🎯 Display scale: ${realisticScale.toFixed(2)} (base: ${baseScale.toFixed(2)}, zoom: ${selectedProduct.zoomFactor.toFixed(2)})`);
                        baseTransform = `scale(${realisticScale})`;
                      } else if (selectedProduct && (selectedProduct.dialSize || selectedProduct.path || selectedProduct.image)) {
                        // Fallback to old inverse fitting logic
                        const watchDialSize = selectedProduct.dialSize || selectedProduct.caseDiameter || 42;
                        const idealWristSize = watchDialSize + 10;
                        const wristSizeRatio = idealWristSize / userWristSize;
                        const scalingFactor = Math.pow(wristSizeRatio, 0.6);
                        const clampedScalingFactor = Math.max(0.7, Math.min(1.4, scalingFactor));
                        baseTransform = `scale(${WATCH_HEIGHT / 25 * clampedScalingFactor})`;
                      } else {
                        // Fallback for products without any sizing data
                        baseTransform = `scale(${WATCH_HEIGHT / 25})`;
                      }
                    }

                    // Apply posture-aware transforms if available
                    if (realisticTryOnInitialized && postureTransforms && activeTab === 'Watches') {
                      // Get posture transform styles
                      const postureStyles = realisticTryOn.getPostureTransformStyles({
                        useGPU: true,
                        unit: 'px'
                      });

                      // Extract rotation and scale from postureTransforms
                      if (postureTransforms.rotation) {
                        baseTransform += ` rotate(${postureTransforms.rotation}deg)`;
                      }

                      if (postureTransforms.rotationX) {
                        baseTransform += ` rotateX(${postureTransforms.rotationX}deg)`;
                      }

                      if (postureTransforms.rotationY) {
                        baseTransform += ` rotateY(${postureTransforms.rotationY}deg)`;
                      }

                      if (postureTransforms.translateX || postureTransforms.translateY) {
                        baseTransform += ` translate(${postureTransforms.translateX || 0}px, ${postureTransforms.translateY || 0}px)`;
                      }
                    }

                    return baseTransform;
                  })(),
                  filter: (() => {
                    let baseFilter = 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))';

                    // Apply posture-aware filters if available
                    if (realisticTryOnInitialized && handPose && activeTab === 'Watches') {
                      const postureFilters = realisticTryOn.getPostureAwareFilters({
                        enableShadow: true,
                        enableBlur: false,
                        enableBrightness: true,
                        enableContrast: true
                      });

                      if (postureFilters.filter) {
                        baseFilter = postureFilters.filter;
                      }
                    }

                    return baseFilter;
                  })()
                }}
                onLoad={(e) => removeBackground(e.target, (() => {
                  switch (activeTab) {
                    case 'Watches':
                      return 'watch';
                    case 'Bracelets':
                      return 'bracelet';
                    case 'Rings':
                      return 'ring';
                    case 'Earrings':
                      return 'earring';
                    default:
                      return 'watch';
                  }
                })())}
              />
              {/* Dial size label - Tangiblee style (no horizontal lines) */}
              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (
                <div style={{
                  position: 'absolute',
                  top: '-25px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: 'white',
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  padding: '2px 6px',
                  borderRadius: '8px',
                  whiteSpace: 'nowrap',
                  pointerEvents: 'none',
                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                  zIndex: 3
                }}>
                  {selectedProduct.dialSize}mm
                </div>
              )}
            </div>
          </div>
            )}

            {/* Stacking display - Watch and Bracelet together */}
            {activeTab === 'Stacking' && (selectedWatch || selectedBracelet) && (
              <>
                {/* Watch in stacking mode */}
                {selectedWatch && (
                  <div style={{
                    ...styles.productPosition,
                    top: isUsingModelImage ? '52%' : '50%',
                    left: isStackingSwapped ? (isUsingModelImage ? '38%' : '42%') : (isUsingModelImage ? '62%' : '56%'), // Watch closer in capture mode
                    transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',
                    width: `${WATCH_WIDTH}%`,
                    height: (() => {
                      const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);
                      if (isLargeWrist) {
                        const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;
                        return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;
                      }
                      return `${WATCH_HEIGHT}%`;
                    })(),
                    clipPath: (() => {
                      const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);
                      return isLargeWrist
                        ? 'ellipse(220px 60px at 50% 50%)'
                        : userWristSize < DEFAULT_WRIST_SIZE
                          ? 'ellipse(220px 60px at 50% 50%)'
                          : 'none';
                    })(),
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      position: 'relative',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <img
                        src={selectedWatch.path}
                        alt="Selected watch"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                          transform: (() => {
                            // Use SUPER REALISTIC fitting logic for stacking mode watches
                            if (selectedWatch && selectedWatch.isRealisticFit && selectedWatch.zoomFactor) {
                              // Use the calculated zoom factor for super realistic display
                              const baseScale = WATCH_HEIGHT / 25;
                              const realisticScale = baseScale * (1 / selectedWatch.zoomFactor);
                              return `scale(${realisticScale})`;
                            } else if (selectedWatch && selectedWatch.dialSize) {
                              // Fallback to old inverse fitting logic
                              const watchDialSize = selectedWatch.dialSize || selectedWatch.caseDiameter || 42;
                              const idealWristSize = watchDialSize + 10;
                              const wristSizeRatio = idealWristSize / userWristSize;
                              const scalingFactor = Math.pow(wristSizeRatio, 0.6);
                              const clampedScalingFactor = Math.max(0.7, Math.min(1.4, scalingFactor));
                              return `scale(${WATCH_HEIGHT / 25 * clampedScalingFactor})`;
                            } else {
                              // Fallback for watches without any sizing data
                              return `scale(${WATCH_HEIGHT / 25})`;
                            }
                          })(),
                          filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'
                        }}
                        onLoad={(e) => removeBackground(e.target, 'watch')}
                      />
                      <div style={{
                        position: 'absolute',
                        bottom: '-30px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontSize: '11px',
                        fontWeight: '600',
                        color: 'white',
                        backgroundColor: 'rgba(45, 140, 136, 0.9)',
                        padding: '3px 8px',
                        borderRadius: '12px',
                        whiteSpace: 'nowrap',
                        pointerEvents: 'none',
                        boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                        zIndex: 2
                      }}>
                        {selectedWatch.dialSize}mm
                      </div>
                    </div>
                  </div>
                )}

                {/* Bracelet in stacking mode */}
                {selectedBracelet && (
                  <div style={{
                    ...styles.productPosition,
                    top: isUsingModelImage ? '52%' : '50%',
                    left: isStackingSwapped ? (isUsingModelImage ? '65%' : '61%') : (isUsingModelImage ? '38%' : '42%'), // Bracelet closer in capture mode
                    transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',
                    width: `${BRACELET_WIDTH}%`,
                    height: `${BRACELET_HEIGHT}%`,
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      position: 'relative',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <img
                        src={selectedBracelet.path}
                        alt="Selected bracelet"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                          transform: (() => {
                            const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 35})`;
                            if (isUsingModelImage) {
                              return `${baseTransform} scaleY(1)`;
                            } else if (isRightHand) {
                              return `${baseTransform} scaleY(-1)`;
                            } else {
                              return `${baseTransform} scaleY(1)`;
                            }
                          })(),
                          filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'
                        }}
                        onLoad={(e) => removeBackground(e.target, 'bracelet')}
                      />
                    </div>
                  </div>
                )}
              </>
            )}
          </>
        )}

        {/* Camera Controls - Only visible when not captured */}
        {!isCaptured && (
          <div style={styles.cameraControls}>
            {/* Camera-style Capture Button - Centered like Tangiblee */}
            <div style={styles.captureButtonWrapper}>
              <button
                style={styles.captureBtn}
                className={isMobile ? 'mobile-capture-btn' : ''}
                onClick={handleCapture}
                aria-label="Capture"
              >
                <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>
              </button>
              <span style={styles.buttonLabel}>Capture</span>
            </div>
          </div>
        )}

        {/* Improved Bottom Action Bar - Only visible when captured */}
        {isCaptured && (
          <div style={styles.improvedBottomBar}>
            {/* Action Buttons Row */}
            <div style={styles.improvedActionButtonsRow}>
              <button
                style={{
                  ...styles.improvedActionButton,
                  backgroundColor: showProductSelection ? '#2D8C88' : '#2D8C88'
                }}
                onClick={() => setShowProductSelection(!showProductSelection)}
                aria-label="Try More Products"
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#1a6b67';
                  e.target.style.transform = 'translateY(-2px) scale(1.02)';
                  e.target.style.boxShadow = '0 6px 20px rgba(45, 140, 136, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#2D8C88';
                  e.target.style.transform = 'translateY(0) scale(1)';
                  e.target.style.boxShadow = '0 4px 12px rgba(45, 140, 136, 0.25)';
                }}
              >
                <div style={styles.actionButtonIcon}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  </svg>
                </div>
                <span style={styles.improvedActionButtonText}>Try More</span>
              </button>

              <button
                style={styles.improvedActionButton}
                onClick={handleTryOnModel}
                aria-label="Change Model"
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#1a6b67';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(45, 140, 136, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#2D8C88';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 6px rgba(45, 140, 136, 0.2)';
                }}
              >
                <div style={styles.actionButtonIcon}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                  </svg>
                </div>
                <span style={styles.improvedActionButtonText}>Change Model</span>
              </button>
            </div>
          </div>
        )}

        {isCaptured && showProductSelection && (
          <div style={styles.unifiedProductPanel}>
            {/* Header with close button */}
            <div style={styles.panelHeader}>
              <div style={styles.panelDragHandle}></div>
              <button
                style={styles.panelCloseBtn}
                onClick={() => setShowProductSelection(false)}
                aria-label="Close panel"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>

            {/* Category selector */}
            <div style={styles.categorySelector}>
              {['Watches', 'Bracelets', 'Rings', 'Earrings'].map((type) => (
                <button
                  key={type}
                  onClick={() => handleTabChange(type)}
                  style={{
                    ...styles.categoryTab,
                    ...(activeTab === type ? styles.categoryTabActive : {})
                  }}
                >
                  {type === 'Watches' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <rect x="7" y="2" width="10" height="20" rx="5"/>
                      <circle cx="12" cy="12" r="3" fill="white"/>
                    </svg>
                  )}
                  {type === 'Bracelets' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <ellipse cx="12" cy="12" rx="8" ry="4" stroke="currentColor" strokeWidth="2" fill="none"/>
                    </svg>
                  )}
                  {type === 'Rings' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <circle cx="12" cy="12" r="7" stroke="currentColor" strokeWidth="2" fill="none"/>
                    </svg>
                  )}
                  {type === 'Earrings' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <circle cx="12" cy="8" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
                      <rect x="11" y="11" width="2" height="7" rx="1"/>
                    </svg>
                  )}
                  <span>{type}</span>
                </button>
              ))}
            </div>

            {/* Stacking status indicator */}
            {activeTab === 'Stacking' && (
              <div style={styles.stackingStatus}>
                <div style={styles.stackingStatusText}>
                  {selectedWatch && selectedBracelet
                    ? '✓ Ready to stack! Both items selected'
                    : `Select ${!selectedWatch ? 'a watch' : ''}${!selectedWatch && !selectedBracelet ? ' and ' : ''}${!selectedBracelet ? 'a bracelet' : ''} to enable stacking`
                  }
                </div>
              </div>
            )}

            {/* Product grid - horizontal scrolling */}
            <div style={styles.productGrid} className="product-grid-scroll">
              {getCurrentProducts().map((product, idx) => {
                let isSelected = false;
                if (activeTab === 'Stacking') {
                  const isWatch = watches.some(w => w.id === product.id);
                  const isBracelet = bracelets.some(b => b.id === product.id);
                  if (isWatch && selectedWatch) isSelected = selectedWatch.id === product.id;
                  else if (isBracelet && selectedBracelet) isSelected = selectedBracelet.id === product.id;
                } else {
                  isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;
                }

                return (
                  <div
                    key={idx}
                    style={{
                      ...styles.productCard,
                      ...(isSelected ? styles.productCardSelected : {})
                    }}
                    onClick={() => handleProductSelect(product)}
                  >
                    <div style={styles.productImageContainer}>
                      <img
                        src={product.path}
                        alt={product.name}
                        style={styles.productImage}
                      />
                      {isSelected && (
                        <div style={styles.selectedIndicator}>
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Action buttons */}
            <div style={styles.actionButtons}>
              <div style={styles.mainButtonsContainer}>
                <button
                  style={{
                    ...styles.actionButton,
                    ...(activeTab === 'Stacking' && selectedWatch && selectedBracelet ? styles.actionButtonActive : {}),
                    opacity: activeTab === 'Stacking' && (!selectedWatch || !selectedBracelet) ? 0.5 : 1
                  }}
                  onClick={() => {
                    if (activeTab === 'Stacking' && selectedWatch && selectedBracelet) {
                      // Already in stacking mode with both items selected - do nothing or show success
                      return;
                    }
                    handleTabChange('Stacking');
                  }}
                  aria-label="Stacking Mode"
                  disabled={activeTab === 'Stacking' && (!selectedWatch || !selectedBracelet)}
                  onMouseEnter={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = '#1a6b67';
                      e.target.style.transform = 'translateY(-1px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = '#2D8C88';
                      e.target.style.transform = 'translateY(0)';
                    }
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <rect x="4" y="6" width="16" height="3" rx="1.5"/>
                    <rect x="4" y="15" width="16" height="3" rx="1.5"/>
                  </svg>
                  <span>Stack</span>
                </button>
                <button
                  style={styles.actionButton}
                  onClick={handleTryOnModel}
                  aria-label="Change Model"
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#1a6b67';
                    e.target.style.transform = 'translateY(-1px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#2D8C88';
                    e.target.style.transform = 'translateY(0)';
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                  </svg>
                  <span>Model</span>
                </button>
              </div>
              <button
                style={styles.refreshButton}
                onClick={() => window.location.reload()}
                aria-label="Refresh Page"
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#e9ecef';
                  e.target.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#f8f9fa';
                  e.target.style.transform = 'scale(1)';
                }}
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Tangiblee-style Vertical Wrist Measurement Lines - Closer to watch */}
      {isCaptured && selectedProduct && activeTab === 'Watches' && (
        <div style={{
          position: 'absolute',
          top: '50%',
          right: '25%', // Moved closer to watch (was 15%)
          transform: 'translateY(-50%)',
          zIndex: 12,
          pointerEvents: 'none'
        }}>
          {/* Vertical measurement line */}
          <div style={{
            width: '1px',
            height: '80px',
            backgroundColor: 'white',
            position: 'relative',
            boxShadow: '0 0 4px rgba(0,0,0,0.5)'
          }}>
            {/* Top measurement cap */}
            <div style={{
              position: 'absolute',
              top: '0',
              left: '-4px',
              width: '9px',
              height: '1px',
              backgroundColor: 'white',
              boxShadow: '0 0 4px rgba(0,0,0,0.5)'
            }} />
            {/* Bottom measurement cap */}
            <div style={{
              position: 'absolute',
              bottom: '0',
              left: '-4px',
              width: '9px',
              height: '1px',
              backgroundColor: 'white',
              boxShadow: '0 0 4px rgba(0,0,0,0.5)'
            }} />
          </div>

          {/* Wrist width label */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '15px',
            transform: 'translateY(-50%)',
            fontSize: '12px',
            fontWeight: '600',
            color: 'white',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            padding: '2px 6px',
            borderRadius: '8px',
            whiteSpace: 'nowrap',
            boxShadow: '0 2px 6px rgba(0,0,0,0.3)'
          }}>
            {userWristSize}mm
          </div>
        </div>
      )}

      {/* Super Realistic Fitting Indicator */}
      {isCaptured && selectedProduct && selectedProduct.isRealisticFit && (
        <div style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          backgroundColor: 'rgba(34, 197, 94, 0.9)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: '600',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
          zIndex: 1000,
          animation: 'fadeIn 0.3s ease-in'
        }}>
          <span style={{ fontSize: '14px' }}>🎯</span>
          <span>Super Realistic Fit</span>
          {selectedProduct.zoomFactor && (
            <span style={{
              fontSize: '10px',
              opacity: 0.8,
              marginLeft: '4px'
            }}>
              {selectedProduct.zoomFactor > 1 ?
                `${(selectedProduct.zoomFactor).toFixed(1)}x zoom` :
                `${(1/selectedProduct.zoomFactor).toFixed(1)}x wide`
              }
            </span>
          )}
        </div>
      )}

      {/* Mobile Wrist Size Button - Top right corner */}
      {isCaptured && (
        <button
          style={styles.wristSizeFloatingBtn}
          className={isMobile ? 'mobile-btn' : ''}
          onClick={() => setShowWristSizeModal(true)}
          aria-label="Adjust wrist size"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
          </svg>
          <span style={styles.wristSizeText}>{userWristSize}mm</span>
        </button>
      )}

      {/* Wrist Size Modal - Mobile-friendly popup */}
      {showWristSizeModal && (
        <div 
          style={styles.modalOverlay} 
          onClick={() => setShowWristSizeModal(false)}
          className="modal-overlay"
        >
          <div 
            style={styles.wristSizeModal} 
            onClick={(e) => e.stopPropagation()}
            className="modal-content"
          >
            <div style={styles.modalHeader}>
              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>
              <button
                style={styles.modalCloseBtn}
                onClick={() => setShowWristSizeModal(false)}
                aria-label="Close"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>

            <div style={styles.modalContent}>
              {/* Wrist Size Slider */}
              <div style={styles.sliderContainer}>
                <label style={styles.sliderLabel}>
                  Wrist Size: {userWristSize}mm
                </label>
                <input
                  type="range"
                  min={userGender === 'men' ? "40" : "35"}
                  max={userGender === 'men' ? "65" : "60"}
                  value={userWristSize}
                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}
                  style={styles.slider}
                />
                <div style={styles.sliderLabels}>
                  <span>{userGender === 'men' ? "40mm" : "35mm"}</span>
                  <span>{userGender === 'men' ? "65mm" : "60mm"}</span>
                </div>

                {/* Quick Size Presets */}
                <div style={styles.presetButtons}>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZE)}
                  >
                    Average ({DEFAULT_WRIST_SIZE}mm)
                  </button>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(MIN_WRIST_SIZE)}
                  >
                    Small ({MIN_WRIST_SIZE}mm)
                  </button>
                  <button
                    style={styles.presetButton}
                    onClick={() => handleWristSizeChange(MAX_WRIST_SIZE)}
                  >
                    Large ({MAX_WRIST_SIZE}mm)
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

// Styles object - Clean, modern design with improved mobile responsiveness
const styles = {
  container: {
    position: 'relative',
    height: 'calc(var(--vh, 1vh) * 100)',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f8f9fa',
    color: '#333',
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    overflow: 'hidden',
    touchAction: 'manipulation',
    WebkitTapHighlightColor: 'transparent',
    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  cameraFeed: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transform: 'scaleX(1)' // Fix for Safari mirroring
  },
  capturedImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    display: 'none',
    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring
  },

  homeBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  backBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  switchContainer: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    zIndex: 10,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '4px',
    padding: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  switchTrack: {
    position: 'absolute',
    top: '12px',
    left: '12px',
    width: '60px',
    height: '32px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: '16px',
    border: '2px solid rgba(255, 255, 255, 0.2)',
    zIndex: 9,
    transition: 'all 0.3s ease'
  },
  switchButton: {
    position: 'relative',
    width: '28px',
    height: '28px',
    borderRadius: '50%',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 11,
    margin: '2px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  switchLabel: {
    fontSize: '12px',
    fontWeight: '700',
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    marginTop: '4px',
    padding: '4px 12px',
    borderRadius: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    letterSpacing: '0.5px'
  },
  countdownDisplay: {
    position: 'absolute',
    top: '35%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '16px 24px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  countdownNumber: {
    fontSize: '72px',
    fontWeight: '900',
    color: '#2D8C88',
    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',
    marginBottom: '8px',
    animation: 'pulse 1s ease-in-out'
  },
  countdownText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'
  },
  statusMessage: {
    position: 'absolute',
    top: '25%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '12px 20px',
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  statusText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(255, 107, 107, 0.9)',
    padding: '12px 20px',
    borderRadius: '25px',
    marginBottom: '8px',
    transition: 'all 0.3s ease'
  },
  statusSubtext: {
    fontSize: '12px',
    fontWeight: '500',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: '6px 12px',
    borderRadius: '15px'
  },
  handGuide: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '80%',
    maxWidth: '400px',
    height: 'auto',
    opacity: 0.8,
    pointerEvents: 'none',
    zIndex: 5,
    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    transition: 'all 0.3s ease'
  },
productPosition: {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: 8,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '25vw', // width controlled
  aspectRatio: '1 / 1.6', // maintain height-to-width ratio (adjust as needed)
  minWidth: '100px',
  minHeight: '160px', // fallback for unsupported aspect-ratio
  pointerEvents: 'none'
}
,
  captureBtn: {
    width: '70px',
    height: '70px',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '3px solid rgba(255, 255, 255, 0.8)',
    outline: 'none',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)'
  },
  captureInner: {
    width: '50px',
    height: '50px',
    backgroundColor: '#2D8C88',
    borderRadius: '50%',
    transition: 'all 0.2s ease',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'
  },
  resetBtn: {
    position: 'absolute',
    bottom: '30px',
    right: '30px',
    width: '50px',
    height: '50px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 10,
    transition: 'all 0.2s ease',
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    padding: 0
  },

  // Wrist Size Floating Button - Follows CSS mobile patterns
  wristSizeFloatingBtn: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    backgroundColor: '#2D8C88',
    color: 'white',
    padding: '12px 16px',
    borderRadius: '24px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    outline: 'none',
    transition: 'all 0.2s ease',
    zIndex: 15,
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',
    minHeight: '48px',
    minWidth: '48px',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  wristSizeText: {
    fontSize: '12px',
    fontWeight: '600'
  },

  // Modal Styles
  modalOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 50,
    padding: '20px',
    backdropFilter: 'blur(5px)',
    WebkitBackdropFilter: 'blur(5px)',
    touchAction: 'none'
  },
  wristSizeModal: {
    backgroundColor: 'white',
    borderRadius: '24px',
    width: '100%',
    maxWidth: '95vw',
    maxHeight: '80vh',
    overflow: 'hidden',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    display: 'flex',
    flexDirection: 'column',
    margin: '10px',
    position: 'relative'
  },
  modalHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '16px 20px 12px 20px',
    borderBottom: '1px solid #f0f0f0'
  },
  modalTitle: {
    fontSize: '18px',
    fontWeight: '700',
    color: '#2D8C88',
    margin: 0
  },
  modalCloseBtn: {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    border: 'none',
    backgroundColor: '#f5f5f5',
    color: '#666',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    outline: 'none'
  },
  modalContent: {
    padding: '16px 20px 20px 20px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    overflowY: 'auto',
    WebkitOverflowScrolling: 'touch',
    maxHeight: 'calc(80vh - 60px)'
  },

  // Mobile styles handled by CSS file
  wristSizeContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px'
  },
  wristSizeTitle: {
    fontSize: '20px',
    fontWeight: '700',
    color: '#2D8C88',
    margin: 0,
    textAlign: 'center'
  },
  wristSizeSubtitle: {
    fontSize: '14px',
    color: '#666',
    margin: 0,
    textAlign: 'center',
    lineHeight: '1.4'
  },
  genderSelection: {
    display: 'flex',
    gap: '12px',
    width: '100%',
    maxWidth: '300px'
  },
  genderButton: {
    flex: 1,
    padding: '12px 16px',
    borderRadius: '10px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '2px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  genderButtonActive: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderColor: '#2D8C88',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'
  },
  sliderContainer: {
    width: '100%',
    maxWidth: '300px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  sliderLabel: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px'
  },
  sizeChange: {
    fontSize: '14px',
    fontWeight: '500',
    color: '#2D8C88',
    backgroundColor: 'rgba(45, 140, 136, 0.1)',
    padding: '2px 8px',
    borderRadius: '12px'
  },
  slider: {
    width: '100%',
    height: '6px',
    borderRadius: '3px',
    background: '#e0e0e0',
    outline: 'none',
    cursor: 'pointer',
    WebkitAppearance: 'none',
    appearance: 'none'
  },
  sliderLabels: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: '12px',
    color: '#999',
    marginTop: '4px'
  },
  presetButtons: {
    display: 'flex',
    gap: '8px',
    width: '100%',
    maxWidth: '300px'
  },
  presetButton: {
    flex: 1,
    padding: '8px 12px',
    borderRadius: '8px',
    fontSize: '12px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  continueButton: {
    width: '100%',
    maxWidth: '300px',
    padding: '14px 24px',
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'
  },

  productSelection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    borderTopLeftRadius: '24px',
    borderTopRightRadius: '24px',
    padding: '16px',
    maxHeight: '85vh',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 20,
    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
    border: 'none',
    transform: 'translateY(0)',
    transition: 'transform 0.3s ease-out',
    overflow: 'hidden',
    touchAction: 'none',
    willChange: 'transform',
    userSelect: 'none',
    WebkitUserSelect: 'none'
  },
  closeBtn: {
    position: 'absolute',
    top: '12px',
    right: '16px',
    color: '#666',
    cursor: 'pointer',
    zIndex: 21,
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    padding: 0
  },
  productTabs: {
    display: 'flex',
    marginBottom: '16px',
    backgroundColor: '#f0f0f0',
    borderRadius: '12px',
    padding: '4px',
    gap: '4px'
  },
  tab: {
    flex: 1,
    textAlign: 'center',
    padding: '12px 16px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    color: '#666',
    outline: 'none',
    border: 'none',
    backgroundColor: 'transparent',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  activeTab: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'
  },
  productScroll: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',
    gap: '12px',
    maxHeight: 'calc(85vh - 120px)',
    overflowY: 'auto',
    paddingBottom: '16px',
    scrollbarWidth: 'thin',
    scrollbarColor: '#ddd #f5f5f5',
    WebkitOverflowScrolling: 'touch'
  },
  productItem: {
    position: 'relative',
    width: '100%',
    aspectRatio: '1/1',
    backgroundColor: '#ffffff',
    borderRadius: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
    padding: '8px',
    outline: 'none',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  productImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    borderRadius: '8px',
    backgroundColor: '#ffffff'
  },
  productLabel: {
    position: 'absolute',
    bottom: '3px',
    left: '3px',
    right: '3px',
    fontSize: '9px',
    color: '#666',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '3px',
    padding: '3px 2px',
    overflow: 'hidden'
  },
  productName: {
    fontSize: '9px',
    fontWeight: '600',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    marginBottom: '1px'
  },
  productSize: {
    fontSize: '8px',
    fontWeight: '500',
    color: '#2D8C88',
    whiteSpace: 'nowrap'
  },
  productType: {
    fontSize: '8px',
    fontWeight: '500',
    color: '#666',
    whiteSpace: 'nowrap'
  },

  // Stacking Switch Styles
  stackingSwitch: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    marginBottom: '16px',
    padding: '12px',
    backgroundColor: '#f8f9fa',
    borderRadius: '12px',
    border: '1px solid #e0e0e0'
  },
  switchLabel: {
    fontSize: '14px',
    fontWeight: '600',
    color: '#333'
  },
  switchContainer: {
    display: 'flex',
    backgroundColor: '#e0e0e0',
    borderRadius: '8px',
    padding: '2px',
    gap: '2px'
  },
  switchOption: {
    padding: '8px 12px',
    fontSize: '12px',
    fontWeight: '500',
    border: 'none',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    backgroundColor: 'transparent',
    color: '#666',
    outline: 'none',
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  switchOptionActive: {
    backgroundColor: '#2D8C88',
    color: 'white',
    boxShadow: '0 1px 3px rgba(45, 140, 136, 0.3)'
  },

  // Small circular stacking switch button
  stackingSwitchButton: {
    position: 'absolute',
    top: '20%',
    right: '145px',
    width: '40px',
    height: '40px',
    backgroundColor: 'rgba(45, 140, 136, 0.9)',
    borderRadius: '50%',
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    outline: 'none',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    zIndex: 15,
    WebkitTapHighlightColor: 'transparent',
    touchAction: 'manipulation'
  },
  dragHandle: {
    width: '40px',
    height: '4px',
    backgroundColor: '#e0e0e0',
    borderRadius: '2px',
    margin: '0 auto 12px',
    cursor: 'grab',
    touchAction: 'none',
    userSelect: 'none',
    WebkitUserSelect: 'none'
  },

  desktopContainer: {
    position: 'relative',
    height: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    padding: '20px'
  },
  qrContainer: {
    backgroundColor: 'white',
    padding: '40px',
    borderRadius: '24px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
    textAlign: 'center',
    maxWidth: '500px',
    width: '100%'
  },
  qrTitle: {
    fontSize: '28px',
    fontWeight: '700',
    color: '#2D8C88',
    marginBottom: '16px'
  },
  qrSubtitle: {
    fontSize: '16px',
    color: '#666',
    marginBottom: '32px',
    lineHeight: '1.5'
  },
  qrWrapper: {
    backgroundColor: 'white',
    padding: '20px',
    borderRadius: '16px',
    display: 'inline-block',
    marginBottom: '24px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
  },
  qrLink: {
    fontSize: '14px',
    color: '#2D8C88',
    marginBottom: '32px',
    wordBreak: 'break-all'
  },

  // Disclaimer Popup Styles
  disclaimerOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    backdropFilter: 'blur(5px)',
    WebkitBackdropFilter: 'blur(5px)'
  },
  disclaimerPopup: {
    backgroundColor: 'white',
    borderRadius: '20px',
    padding: '0',
    maxWidth: '400px',
    width: '90%',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.2)'
  },
  disclaimerContent: {
    padding: '32px 24px',
    textAlign: 'center'
  },
  disclaimerTitle: {
    fontSize: '24px',
    fontWeight: '700',
    color: '#2D8C88',
    marginBottom: '24px',
    margin: '0 0 24px 0'
  },
  disclaimerPoints: {
    marginBottom: '32px'
  },
  disclaimerPoint: {
    fontSize: '16px',
    color: '#333',
    lineHeight: '1.6',
    marginBottom: '16px',
    margin: '0 0 16px 0',
    textAlign: 'center'
  },
  disclaimerButton: {
    backgroundColor: '#2D8C88',
    color: 'white',
    border: 'none',
    borderRadius: '12px',
    padding: '14px 32px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'
  },

  // Clean Instruction Styles
  instructionContainer: {
    position: 'absolute',
    top: '28%', // moved further down from 20%
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 19,
    pointerEvents: 'none'
  },
  instructionText: {
    fontSize: '14px',
    fontWeight: '500',
    color: 'white',
    textAlign: 'center',
    padding: '8px 16px',
    borderRadius: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    lineHeight: '1.4',
    maxWidth: '260px'
  },

  // Add new styles for smaller status message
  statusMessageSmall: {
    position: 'absolute',
    top: '13%', // move up so it's above the SVG shape
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '6px 12px',
    borderRadius: '10px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
  },
  statusTextSmall: {
    fontSize: '12px',
    fontWeight: '500',
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(255, 107, 107, 0.8)',
    padding: '6px 12px',
    borderRadius: '15px',
    marginBottom: '4px',
    transition: 'all 0.3s ease'
  },
  statusSubtextSmall: {
    fontSize: '10px',
    fontWeight: '400',
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: '4px 8px',
    borderRadius: '10px'
  },

  // Clean Branding - Tangiblee style
  brandingContainerTangiblee: {
    position: 'absolute',
    top: '32px',
    left: '12px',
    display: 'flex',
    alignItems: 'center',
    gap: '14px',
    zIndex: 15,
    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent
    padding: '0',
    borderRadius: '0',
    boxShadow: 'none',
  },
  brandingLogoTangiblee: {
    width: '38px',
    height: '38px',
    objectFit: 'contain',
    display: 'inline-block',
    verticalAlign: 'middle',
  },
  brandingTextStacked: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    lineHeight: 1.1,
  },
  poweredByText: {
    fontSize: '15px',
    color: 'white',
    fontWeight: 400,
    opacity: 0.85,
    letterSpacing: '0.01em',
    marginBottom: '1px',
  },
  viatryonText: {
    fontSize: '28px',
    color: 'white',
    fontWeight: 600,
    letterSpacing: '0.01em',
    marginTop: '0',
  },

  // Camera Controls - Centered capture button like Tangiblee
  cameraControls: {
    position: 'absolute',
    bottom: '30px',
    left: '50%',
    transform: 'translateX(-50%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 15
  },
  captureButtonWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '8px'
  },
  buttonLabel: {
    fontSize: '14px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 2px 4px rgba(0, 0, 0, 0.8)',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: '6px 12px',
    borderRadius: '12px',
    pointerEvents: 'none',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  modelBtn: {
    width: '60px',
    height: '60px',
    backgroundColor: 'rgba(45, 140, 136, 0.9)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)'
  },

  // Product Arrow Button - Shows after capture/model mode
  productArrowBtn: {
    position: 'absolute',
    bottom: '30px',
    right: '30px',
    width: '60px',
    height: '60px',
    backgroundColor: 'rgba(45, 140, 136, 0.9)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    zIndex: 15
  },

  // Back Arrow Button - Top Left
  backArrowBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    width: '44px',
    height: '44px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textDecoration: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    zIndex: 20
  },

  // Clean Branding - Tangiblee style
  brandingContainerTangiblee: {
    position: 'absolute',
    top: '32px',
    left: '16px', // Moved more to the left
    display: 'flex',
    gap: '14px',
    zIndex: 15,
    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent
    padding: '0',
    borderRadius: '0',
    boxShadow: 'none',
  },
  brandingLogoTangiblee: {
    width: '45px', // Made logo slightly bigger
    height: '45px',
    objectFit: 'contain',
    display: 'inline-block',
    verticalAlign: 'middle',
  },
  brandingTextStacked: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    lineHeight: 1.2, // Increased line height for more vertical space
  },
  poweredByText: {
    fontSize: '16px', // Made text slightly bigger
    color: 'white',
    fontWeight: 400,
    opacity: 0.85,
    letterSpacing: '0.01em',
    marginBottom: '2px', // Increased spacing between texts
  },
  viatryonText: {
    fontSize: '28px', // Made text bigger
    color: 'white',
    fontWeight: 600,
    letterSpacing: '0.01em',
    marginTop: '0',
  },

  // Back Arrow Button - Top Left
  backArrowBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    width: '44px',
    height: '44px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    zIndex: 20
  },

  // Tiny Branding - Between back arrow and autocapture switch
  brandingContainerBetween: {
    position: 'absolute',
    top: '25px',
    left: '65px', // Moved more to the left
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    zIndex: 15,
    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent
    padding: '0',
    borderRadius: '0',
    boxShadow: 'none',
  },
  brandingLogoTiny: {
    width: '35px', // Made logo bigger
    height: '35px',
    objectFit: 'contain',
    display: 'inline-block',
    verticalAlign: 'middle',
  },
  brandingTextTiny: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    lineHeight: 1.2, // Increased line height for more vertical space
  },
  poweredByTextTiny: {
    fontSize: '14px', // Made text bigger
    color: 'white',
    fontWeight: 400,
    opacity: 0.85,
    letterSpacing: '0.01em',
    marginBottom: '2px', // Increased spacing between texts
  },
  viatryonTextTiny: {
    fontSize: '14px', // Made text bigger
    color: 'white',
    fontWeight: 600,
    letterSpacing: '0.01em',
    marginTop: '0',
  },

  // Tangiblee-style Bottom Action Bar
  tangibleeBottomBar: {
    position: 'absolute',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: '25px',
    padding: '8px 16px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  actionButtonsRow: {
    display: 'flex',
    alignItems: 'center',
    gap: '20px'
  },
  actionButton: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '4px',
    backgroundColor: 'transparent',
    border: 'none',
    color: 'white',
    cursor: 'pointer',
    padding: '8px 12px',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    outline: 'none'
  },
  actionButtonText: {
    fontSize: '11px',
    fontWeight: '500',
    color: 'white',
    opacity: 0.9
  },

  // Improved Tangiblee-style Product Panel
  tangibleeProductPanel: {
    position: 'absolute',
    bottom: '0',
    left: '0',
    right: '0',
    backgroundColor: 'white',
    borderTopLeftRadius: '24px',
    borderTopRightRadius: '24px',
    boxShadow: '0 -8px 32px rgba(0, 0, 0, 0.2)',
    zIndex: 10,
    maxHeight: '45vh',
    overflow: 'hidden',
    animation: 'slideUp 0.3s ease-out'
  },
  tangibleeCategoryTabs: {
    display: 'flex',
    padding: '20px 20px 12px 20px',
    gap: '12px',
    borderBottom: '1px solid #f0f0f0',
    overflowX: 'auto',
    WebkitOverflowScrolling: 'touch',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none'
  },
  tangibleeTab: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px 20px',
    backgroundColor: '#f8f9fa',
    border: 'none',
    borderRadius: '25px',
    fontSize: '14px',
    fontWeight: '600',
    color: '#666',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    whiteSpace: 'nowrap',
    outline: 'none',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
  },
  tangibleeActiveTab: {
    backgroundColor: '#2D8C88',
    color: 'white',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 16px rgba(45, 140, 136, 0.3)'
  },
  tangibleeProductScroll: {
    display: 'flex',
    padding: '20px',
    gap: '16px',
    overflowX: 'auto',
    WebkitOverflowScrolling: 'touch',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    scrollBehavior: 'smooth'
  },
  tangibleeProductCard: {
    minWidth: '90px',
    backgroundColor: 'white',
    borderRadius: '16px',
    border: '2px solid transparent',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    overflow: 'hidden',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    position: 'relative'
  },
  tangibleeProductImageContainer: {
    width: '90px',
    height: '90px',
    backgroundColor: '#f8f9fa',
    borderRadius: '14px',
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  },
  tangibleeProductImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    transition: 'transform 0.3s ease'
  },
  tangibleeProductInfo: {
    padding: '12px 8px',
    textAlign: 'center'
  },
  tangibleeProductName: {
    fontSize: '12px',
    fontWeight: '600',
    color: '#333',
    marginBottom: '4px',
    lineHeight: 1.3,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
  },
  tangibleeProductSize: {
    fontSize: '11px',
    color: '#666',
    fontWeight: '500',
    backgroundColor: '#f0f0f0',
    padding: '2px 8px',
    borderRadius: '12px',
    display: 'inline-block'
  },
  tangibleeProductStatus: {
    padding: '16px 20px',
    borderTop: '1px solid #f0f0f0',
    backgroundColor: '#fafafa'
  },
  tangibleeCurrentProduct: {
    fontSize: '18px',
    fontWeight: '700',
    color: '#333',
    marginBottom: '6px',
    textAlign: 'center'
  },
  tangibleeOutOfStock: {
    fontSize: '14px',
    color: '#e74c3c',
    fontWeight: '500'
  },

  // Improved UI Styles for Better UX
  improvedWristDisplay: {
    position: 'absolute',
    bottom: '120px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    color: 'white',
    padding: '16px 24px',
    borderRadius: '20px',
    textAlign: 'center',
    zIndex: 15,
    boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.15)',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    minWidth: '200px'
  },
  wristDisplayHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    fontSize: '13px',
    opacity: 0.9,
    marginBottom: '8px',
    fontWeight: '500'
  },
  wristDisplayLabel: {
    fontSize: '13px',
    fontWeight: '500'
  },
  wristDisplayValue: {
    fontSize: '24px',
    fontWeight: '700',
    marginBottom: '8px',
    color: '#2D8C88'
  },
  wristDisplayAction: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '11px',
    opacity: 0.8,
    fontWeight: '600',
    letterSpacing: '0.5px'
  },

  // Improved Bottom Action Bar
  improvedBottomBar: {
    position: 'absolute',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: '30px',
    padding: '12px 20px',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
    border: '1px solid rgba(255, 255, 255, 0.15)'
  },
  improvedActionButtonsRow: {
    display: 'flex',
    alignItems: 'center',
    gap: '16px'
  },
  improvedActionButton: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '4px',
    backgroundColor: '#2D8C88',
    border: 'none',
    color: 'white',
    cursor: 'pointer',
    padding: '8px 12px',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    outline: 'none',
    minWidth: '60px',
    boxShadow: '0 2px 6px rgba(45, 140, 136, 0.2)',
    minHeight: '50px',
    justifyContent: 'center'
  },
  actionButtonIcon: {
    width: '24px',
    height: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    marginBottom: '1px'
  },
  improvedActionButtonText: {
    fontSize: '10px',
    fontWeight: '600',
    color: 'white',
    opacity: 0.95,
    letterSpacing: '0.1px',
    textAlign: 'center'
  },

  // Unified Product Panel Styles
  unifiedProductPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: '24px',
    borderTopRightRadius: '24px',
    boxShadow: '0 -8px 32px rgba(0, 0, 0, 0.15)',
    zIndex: 30,
    maxHeight: '70vh',
    minHeight: '300px',
    display: 'flex',
    flexDirection: 'column',
    animation: 'slideUp 0.3s ease-out',
    overflow: 'hidden'
  },

  panelHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '16px 20px 8px 20px',
    borderBottom: '1px solid #f0f0f0'
  },

  panelDragHandle: {
    width: '40px',
    height: '4px',
    backgroundColor: '#e0e0e0',
    borderRadius: '2px',
    position: 'absolute',
    top: '8px',
    left: '50%',
    transform: 'translateX(-50%)'
  },

  panelCloseBtn: {
    background: 'none',
    border: 'none',
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    color: '#666',
    transition: 'all 0.2s',
    marginLeft: 'auto'
  },

  categorySelector: {
    display: 'flex',
    gap: '8px',
    padding: '0 20px 16px 20px',
    overflowX: 'auto',
    WebkitOverflowScrolling: 'touch',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none'
  },

  categoryTab: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    borderRadius: '20px',
    border: '2px solid #e0e0e0',
    backgroundColor: 'white',
    color: '#666',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s',
    whiteSpace: 'nowrap',
    minWidth: 'fit-content'
  },

  categoryTabActive: {
    backgroundColor: '#2D8C88',
    borderColor: '#2D8C88',
    color: 'white',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.2)'
  },

  stackingStatus: {
    padding: '8px 20px',
    backgroundColor: '#f8f9fa',
    borderTop: '1px solid #e0e0e0',
    borderBottom: '1px solid #e0e0e0'
  },

  stackingStatusText: {
    fontSize: '12px',
    fontWeight: '500',
    color: '#666',
    textAlign: 'center',
    lineHeight: '1.3'
  },

  productGrid: {
    display: 'flex',
    gap: '12px',
    padding: '0 20px 16px 20px',
    overflowX: 'auto',
    overflowY: 'hidden',
    WebkitOverflowScrolling: 'touch',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none'
  },

  productCard: {
    backgroundColor: 'white',
    border: '2px solid #f0f0f0',
    borderRadius: '16px',
    padding: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    minWidth: '80px',
    maxWidth: '80px',
    minHeight: '80px',
    flexShrink: 0
  },

  productCardSelected: {
    borderColor: '#2D8C88',
    backgroundColor: '#f0fffe',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.15)'
  },

  productImageContainer: {
    position: 'relative',
    width: '64px',
    height: '64px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },

  productImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    borderRadius: '8px'
  },

  selectedIndicator: {
    position: 'absolute',
    top: '-6px',
    right: '-6px',
    width: '22px',
    height: '22px',
    backgroundColor: '#2D8C88',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
    border: '2px solid white'
  },

  actionButtons: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: '12px',
    padding: '16px 20px 20px 20px',
    borderTop: '1px solid #f0f0f0'
  },

  mainButtonsContainer: {
    display: 'flex',
    gap: '8px',
    flex: 1
  },

  actionButton: {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '6px',
    padding: '16px 12px',
    borderRadius: '25px',
    border: 'none',
    backgroundColor: '#2D8C88',
    color: 'white',
    fontSize: '13px',
    fontWeight: '700',
    cursor: 'pointer',
    transition: 'all 0.3s',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.25)',
    minHeight: '50px'
  },

  actionButtonActive: {
    backgroundColor: '#1a6b67',
    boxShadow: '0 6px 16px rgba(45, 140, 136, 0.4)',
    transform: 'translateY(-1px)'
  },

  refreshButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '50px',
    height: '50px',
    borderRadius: '25px',
    border: 'none',
    backgroundColor: '#f8f9fa',
    color: '#666',
    cursor: 'pointer',
    transition: 'all 0.3s',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    flexShrink: 0
  }
};

export default VirtualTryon;