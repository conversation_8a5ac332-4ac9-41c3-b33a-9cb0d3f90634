/**
 * OpenCV-based Image Warping Module
 * 
 * This module provides advanced image warping capabilities using OpenCV.js
 * to transform watch images to match wrist angle, rotation, and perspective.
 */

// OpenCV.js will be loaded dynamically
let cv = null;
let isOpenCVReady = false;

/**
 * Initialize OpenCV.js
 * @returns {Promise<boolean>} - Whether OpenCV was successfully initialized
 */
export const initOpenCV = () => {
  return new Promise((resolve) => {
    if (isOpenCVReady) {
      resolve(true);
      return;
    }

    // Check if OpenCV is already loaded
    if (window.cv && window.cv.Mat) {
      cv = window.cv;
      isOpenCVReady = true;
      resolve(true);
      return;
    }

    // Load OpenCV.js dynamically
    const script = document.createElement('script');
    script.src = 'https://docs.opencv.org/4.8.0/opencv.js';
    script.async = true;
    
    script.onload = () => {
      // Wait for OpenCV to be ready
      const checkOpenCV = () => {
        if (window.cv && window.cv.Mat) {
          cv = window.cv;
          isOpenCVReady = true;
          console.log('OpenCV.js loaded successfully');
          resolve(true);
        } else {
          setTimeout(checkOpenCV, 100);
        }
      };
      checkOpenCV();
    };
    
    script.onerror = () => {
      console.error('Failed to load OpenCV.js');
      resolve(false);
    };
    
    document.head.appendChild(script);
  });
};

/**
 * Warp a watch image to match wrist pose
 * @param {HTMLImageElement|HTMLCanvasElement} sourceImage - Source watch image
 * @param {Object} handPose - 2.5D hand pose data
 * @param {Object} options - Warping options
 * @returns {Promise<HTMLCanvasElement>} - Warped image canvas
 */
export const warpWatchImage = async (sourceImage, handPose, options = {}) => {
  if (!isOpenCVReady) {
    console.error('OpenCV not initialized');
    return null;
  }

  const {
    outputWidth = 512,
    outputHeight = 512,
    interpolation = cv.INTER_LINEAR,
    borderMode = cv.BORDER_TRANSPARENT
  } = options;

  try {
    // Create canvas for source image
    const sourceCanvas = document.createElement('canvas');
    const sourceCtx = sourceCanvas.getContext('2d');
    sourceCanvas.width = sourceImage.width || sourceImage.naturalWidth;
    sourceCanvas.height = sourceImage.height || sourceImage.naturalHeight;
    sourceCtx.drawImage(sourceImage, 0, 0);

    // Convert to OpenCV Mat
    const src = cv.imread(sourceCanvas);
    
    // Calculate transformation matrix from hand pose
    const transformMatrix = calculateTransformMatrix(handPose, src.cols, src.rows);
    
    // Create output Mat
    const dst = new cv.Mat();
    const dsize = new cv.Size(outputWidth, outputHeight);
    
    // Apply perspective transformation
    cv.warpPerspective(src, dst, transformMatrix, dsize, interpolation, borderMode);
    
    // Convert back to canvas
    const outputCanvas = document.createElement('canvas');
    outputCanvas.width = outputWidth;
    outputCanvas.height = outputHeight;
    cv.imshow(outputCanvas, dst);
    
    // Clean up
    src.delete();
    dst.delete();
    transformMatrix.delete();
    
    return outputCanvas;
  } catch (error) {
    console.error('Error warping watch image:', error);
    return null;
  }
};

/**
 * Calculate transformation matrix from hand pose
 * @param {Object} handPose - 2.5D hand pose data
 * @param {number} srcWidth - Source image width
 * @param {number} srcHeight - Source image height
 * @returns {cv.Mat} - 3x3 transformation matrix
 */
const calculateTransformMatrix = (handPose, srcWidth, srcHeight) => {
  if (!handPose || !handPose.wristPlane) {
    // Return identity matrix if no pose data
    return cv.matFromArray(3, 3, cv.CV_64FC1, [
      1, 0, 0,
      0, 1, 0,
      0, 0, 1
    ]);
  }

  const { wristPlane, handOrientation, wristDimensions } = handPose;
  
  // Define source points (corners of the watch image)
  const srcPoints = [
    [0, 0],
    [srcWidth, 0],
    [srcWidth, srcHeight],
    [0, srcHeight]
  ];
  
  // Calculate destination points based on wrist pose
  const dstPoints = calculateDestinationPoints(
    srcPoints,
    wristPlane,
    handOrientation,
    wristDimensions,
    srcWidth,
    srcHeight
  );
  
  // Create OpenCV point arrays
  const srcPointsMat = cv.matFromArray(4, 1, cv.CV_32FC2, srcPoints.flat());
  const dstPointsMat = cv.matFromArray(4, 1, cv.CV_32FC2, dstPoints.flat());
  
  // Calculate perspective transformation matrix
  const transformMatrix = cv.getPerspectiveTransform(srcPointsMat, dstPointsMat);
  
  // Clean up
  srcPointsMat.delete();
  dstPointsMat.delete();
  
  return transformMatrix;
};

/**
 * Calculate destination points for perspective transformation
 * @param {Array} srcPoints - Source image corner points
 * @param {Object} wristPlane - Wrist plane data
 * @param {Object} handOrientation - Hand orientation data
 * @param {Object} wristDimensions - Wrist dimensions
 * @param {number} srcWidth - Source image width
 * @param {number} srcHeight - Source image height
 * @returns {Array} - Destination points
 */
const calculateDestinationPoints = (
  srcPoints,
  wristPlane,
  handOrientation,
  wristDimensions,
  srcWidth,
  srcHeight
) => {
  // Calculate scale factor based on wrist dimensions
  const scaleX = wristDimensions.width / srcWidth;
  const scaleY = wristDimensions.height / srcHeight;
  
  // Calculate rotation angles
  const pitch = handOrientation.pitch * (Math.PI / 180);
  const yaw = handOrientation.yaw * (Math.PI / 180);
  const roll = handOrientation.roll * (Math.PI / 180);
  
  // Calculate center point
  const centerX = srcWidth / 2;
  const centerY = srcHeight / 2;
  
  // Transform each source point
  const dstPoints = srcPoints.map(([x, y]) => {
    // Translate to origin
    let tx = x - centerX;
    let ty = y - centerY;
    
    // Apply scaling
    tx *= scaleX;
    ty *= scaleY;
    
    // Apply 3D rotations
    // Rotation around X-axis (pitch)
    let tz = 0;
    let newY = ty * Math.cos(pitch) - tz * Math.sin(pitch);
    let newZ = ty * Math.sin(pitch) + tz * Math.cos(pitch);
    ty = newY;
    tz = newZ;
    
    // Rotation around Y-axis (yaw)
    let newX = tx * Math.cos(yaw) + tz * Math.sin(yaw);
    newZ = -tx * Math.sin(yaw) + tz * Math.cos(yaw);
    tx = newX;
    tz = newZ;
    
    // Rotation around Z-axis (roll)
    newX = tx * Math.cos(roll) - ty * Math.sin(roll);
    newY = tx * Math.sin(roll) + ty * Math.cos(roll);
    tx = newX;
    ty = newY;
    
    // Apply perspective projection
    const perspectiveFactor = 1 + tz * 0.001; // Adjust this factor for perspective strength
    tx /= perspectiveFactor;
    ty /= perspectiveFactor;
    
    // Translate back and add wrist center offset
    const finalX = tx + centerX + (wristPlane.center.x * srcWidth);
    const finalY = ty + centerY + (wristPlane.center.y * srcHeight);
    
    return [finalX, finalY];
  });
  
  return dstPoints;
};

/**
 * Apply barrel distortion to simulate watch curvature
 * @param {HTMLCanvasElement} sourceCanvas - Source canvas
 * @param {Object} options - Distortion options
 * @returns {HTMLCanvasElement} - Distorted canvas
 */
export const applyBarrelDistortion = (sourceCanvas, options = {}) => {
  if (!isOpenCVReady) {
    console.error('OpenCV not initialized');
    return sourceCanvas;
  }

  const {
    k1 = 0.1,  // Radial distortion coefficient
    k2 = 0.01, // Second radial distortion coefficient
    centerX = 0.5, // Distortion center X (normalized)
    centerY = 0.5  // Distortion center Y (normalized)
  } = options;

  try {
    // Convert to OpenCV Mat
    const src = cv.imread(sourceCanvas);
    const dst = new cv.Mat();
    
    // Create camera matrix (identity for simplicity)
    const cameraMatrix = cv.matFromArray(3, 3, cv.CV_64FC1, [
      src.cols, 0, src.cols * centerX,
      0, src.rows, src.rows * centerY,
      0, 0, 1
    ]);
    
    // Create distortion coefficients
    const distCoeffs = cv.matFromArray(1, 4, cv.CV_64FC1, [k1, k2, 0, 0]);
    
    // Apply undistortion (which creates barrel distortion when applied in reverse)
    cv.undistort(src, dst, cameraMatrix, distCoeffs);
    
    // Convert back to canvas
    const outputCanvas = document.createElement('canvas');
    outputCanvas.width = sourceCanvas.width;
    outputCanvas.height = sourceCanvas.height;
    cv.imshow(outputCanvas, dst);
    
    // Clean up
    src.delete();
    dst.delete();
    cameraMatrix.delete();
    distCoeffs.delete();
    
    return outputCanvas;
  } catch (error) {
    console.error('Error applying barrel distortion:', error);
    return sourceCanvas;
  }
};

/**
 * Apply motion blur to simulate hand movement
 * @param {HTMLCanvasElement} sourceCanvas - Source canvas
 * @param {Object} options - Motion blur options
 * @returns {HTMLCanvasElement} - Blurred canvas
 */
export const applyMotionBlur = (sourceCanvas, options = {}) => {
  if (!isOpenCVReady) {
    console.error('OpenCV not initialized');
    return sourceCanvas;
  }

  const {
    angle = 0,    // Blur angle in degrees
    length = 5    // Blur length in pixels
  } = options;

  try {
    // Convert to OpenCV Mat
    const src = cv.imread(sourceCanvas);
    const dst = new cv.Mat();
    
    // Create motion blur kernel
    const kernel = createMotionBlurKernel(angle, length);
    
    // Apply filter
    cv.filter2D(src, dst, cv.CV_8U, kernel);
    
    // Convert back to canvas
    const outputCanvas = document.createElement('canvas');
    outputCanvas.width = sourceCanvas.width;
    outputCanvas.height = sourceCanvas.height;
    cv.imshow(outputCanvas, dst);
    
    // Clean up
    src.delete();
    dst.delete();
    kernel.delete();
    
    return outputCanvas;
  } catch (error) {
    console.error('Error applying motion blur:', error);
    return sourceCanvas;
  }
};

/**
 * Create motion blur kernel
 * @param {number} angle - Blur angle in degrees
 * @param {number} length - Blur length in pixels
 * @returns {cv.Mat} - Motion blur kernel
 */
const createMotionBlurKernel = (angle, length) => {
  const rad = angle * (Math.PI / 180);
  const dx = Math.cos(rad);
  const dy = Math.sin(rad);
  
  const size = Math.max(3, length * 2 + 1);
  const kernel = cv.Mat.zeros(size, size, cv.CV_32FC1);
  
  const centerX = Math.floor(size / 2);
  const centerY = Math.floor(size / 2);
  
  for (let i = 0; i < length; i++) {
    const x = Math.round(centerX + dx * i);
    const y = Math.round(centerY + dy * i);
    
    if (x >= 0 && x < size && y >= 0 && y < size) {
      kernel.floatPtr(y, x)[0] = 1.0 / length;
    }
  }
  
  return kernel;
};

/**
 * Apply depth of field blur based on hand pose
 * @param {HTMLCanvasElement} sourceCanvas - Source canvas
 * @param {Object} handPose - Hand pose data
 * @param {Object} options - DOF options
 * @returns {HTMLCanvasElement} - Blurred canvas
 */
export const applyDepthOfField = (sourceCanvas, handPose, options = {}) => {
  if (!isOpenCVReady) {
    console.error('OpenCV not initialized');
    return sourceCanvas;
  }

  const {
    focusDistance = 0.5, // Focus distance (0-1)
    blurStrength = 5     // Maximum blur strength
  } = options;

  try {
    // Convert to OpenCV Mat
    const src = cv.imread(sourceCanvas);
    const dst = new cv.Mat();
    
    // Calculate blur amount based on depth
    const depthInfo = handPose?.depthInfo;
    if (!depthInfo) {
      // No depth info, return original
      return sourceCanvas;
    }
    
    const depthDifference = Math.abs(depthInfo.avgWristZ - focusDistance);
    const blurAmount = Math.min(blurStrength, depthDifference * blurStrength * 10);
    
    if (blurAmount > 0.5) {
      // Apply Gaussian blur
      const ksize = new cv.Size(
        Math.max(3, Math.floor(blurAmount) * 2 + 1),
        Math.max(3, Math.floor(blurAmount) * 2 + 1)
      );
      cv.GaussianBlur(src, dst, ksize, blurAmount, blurAmount);
    } else {
      // No blur needed
      src.copyTo(dst);
    }
    
    // Convert back to canvas
    const outputCanvas = document.createElement('canvas');
    outputCanvas.width = sourceCanvas.width;
    outputCanvas.height = sourceCanvas.height;
    cv.imshow(outputCanvas, dst);
    
    // Clean up
    src.delete();
    dst.delete();
    
    return outputCanvas;
  } catch (error) {
    console.error('Error applying depth of field:', error);
    return sourceCanvas;
  }
};

/**
 * Create a complete warped and enhanced watch image
 * @param {HTMLImageElement|HTMLCanvasElement} sourceImage - Source watch image
 * @param {Object} handPose - 2.5D hand pose data
 * @param {Object} options - Enhancement options
 * @returns {Promise<HTMLCanvasElement>} - Enhanced watch image
 */
export const createEnhancedWatchImage = async (sourceImage, handPose, options = {}) => {
  const {
    enablePerspective = true,
    enableBarrelDistortion = true,
    enableMotionBlur = false,
    enableDepthOfField = true,
    outputWidth = 512,
    outputHeight = 512
  } = options;

  let currentCanvas = null;

  try {
    // Step 1: Apply perspective transformation
    if (enablePerspective && handPose) {
      currentCanvas = await warpWatchImage(sourceImage, handPose, {
        outputWidth,
        outputHeight
      });
    } else {
      // Create canvas from source image
      currentCanvas = document.createElement('canvas');
      const ctx = currentCanvas.getContext('2d');
      currentCanvas.width = outputWidth;
      currentCanvas.height = outputHeight;
      ctx.drawImage(sourceImage, 0, 0, outputWidth, outputHeight);
    }

    if (!currentCanvas) {
      throw new Error('Failed to create initial canvas');
    }

    // Step 2: Apply barrel distortion for watch curvature
    if (enableBarrelDistortion) {
      const distortedCanvas = applyBarrelDistortion(currentCanvas, {
        k1: 0.05,
        k2: 0.01
      });
      currentCanvas = distortedCanvas;
    }

    // Step 3: Apply motion blur if hand is moving
    if (enableMotionBlur && handPose?.handOrientation) {
      const motionBlurredCanvas = applyMotionBlur(currentCanvas, {
        angle: handPose.handOrientation.roll,
        length: 3
      });
      currentCanvas = motionBlurredCanvas;
    }

    // Step 4: Apply depth of field
    if (enableDepthOfField && handPose) {
      const dofCanvas = applyDepthOfField(currentCanvas, handPose, {
        focusDistance: 0.5,
        blurStrength: 2
      });
      currentCanvas = dofCanvas;
    }

    return currentCanvas;
  } catch (error) {
    console.error('Error creating enhanced watch image:', error);
    
    // Fallback: return simple scaled image
    const fallbackCanvas = document.createElement('canvas');
    const ctx = fallbackCanvas.getContext('2d');
    fallbackCanvas.width = outputWidth;
    fallbackCanvas.height = outputHeight;
    ctx.drawImage(sourceImage, 0, 0, outputWidth, outputHeight);
    return fallbackCanvas;
  }
};

export default {
  initOpenCV,
  warpWatchImage,
  applyBarrelDistortion,
  applyMotionBlur,
  applyDepthOfField,
  createEnhancedWatchImage
};
